<script lang="ts">
  import { augmentStore } from '$lib/stores/augment.svelte'
  import rendererLogger from '$lib/utils/logger'
  import toast from 'svelte-french-toast'
  import { goto } from '$app/navigation'

  let dialogRef = $state<HTMLDialogElement>()
  let tokenInfo = $state({
    token: '',
    tenantUrl: ''
  })
  let parseLoading = $state<boolean>(false)

  const generateLoginUrl = async () => {
    const res = await window.api.generateLoginUrl()
    if (!res.success) {
      return toast.error(res.message)
    }
    augmentStore.loginUrl = res.data
    toast.success('生成成功,有效期10分钟')
  }

  async function parseAuthUrl() {
    const text = await window.api.clipboard.readText()
    if (
      text?.startsWith('vscode://augment.vscode-augment/auth/result?code=') ||
      text?.startsWith('cursor://augment.vscode-augment/auth/result?code=')
    ) {
      augmentStore.authUrl = text
      window.api.clipboard.writeText('')
      toast.success('自动粘贴', { icon: '🍔' })
    }
  }

  async function generateToken() {
    try {
      parseLoading = true
      const url = new URL(augmentStore.authUrl)
      const code = url.searchParams.get('code')
      const redirectUri = url.protocol + '//' + url.hostname + url.pathname
      const tenantUrl = url.searchParams.get('tenant_url')
      if (!code || !tenantUrl) {
        return toast.error('链接格式不正确')
      }
      rendererLogger.info(
        '解析中: ',
        JSON.stringify(
          {
            code,
            tenantUrl,
            redirectUri,
            authUrl: $state.snapshot(augmentStore.authUrl)
          },
          null,
          2
        )
      )
      const res = await window.api.verifyCode(code, tenantUrl, redirectUri)
      if (!res.success) {
        rendererLogger.error('解析失败: ', res.message)
        return toast.error(res.message)
      }
      tokenInfo = {
        token: res.data.token,
        tenantUrl
      }
      rendererLogger.info('解析成功, token:', JSON.stringify(tokenInfo, null, 2))
      window.api.clipboard.writeText(JSON.stringify(tokenInfo, null, 2))
      openDialog()
    } catch (error) {
      rendererLogger.error('解析失败: ', error)
      toast.error('解析失败: ' + error.message)
    } finally {
      parseLoading = false
      augmentStore.authUrl = ''
      augmentStore.loginUrl = ''
    }
  }

  function openDialog() {
    if (!dialogRef.open) {
      dialogRef.showModal()
    }
  }

  function closeDialog() {
    if (!dialogRef.open) {
      return
    }
    dialogRef.close()
  }

  function goToAccount() {
    closeDialog()
    goto('/main/accounts?token=' + tokenInfo.token + '&tenantUrl=' + tokenInfo.tenantUrl)
  }
</script>

<div class="h-full">
  <div class="divider divider-info">登录相关</div>
  <div class="flex w-full flex-col gap-2">
    <div class="flex w-full flex-col justify-center gap-2">
      <button class="btn btn-outline btn-primary flex-shrink-0" onclick={generateLoginUrl}
        >生成登录链接</button
      >
      {#if augmentStore.loginUrl}
        <button
          class="link link-primary flex-1 text-left break-all"
          onclick={() => {
            window.api.clipboard.writeText(augmentStore.loginUrl)
            toast.success('复制成功')
          }}
        >
          {augmentStore.loginUrl}
        </button>
      {/if}
    </div>
    <div>
      <input
        type="text"
        class="input input-info"
        bind:value={augmentStore.authUrl}
        placeholder="请输入网页上登录成功后的链接"
        onfocus={parseAuthUrl}
      />
      <button
        class="btn btn-outline btn-info ml-2"
        onclick={generateToken}
        disabled={parseLoading || !augmentStore.authUrl}
      >
        <span class={parseLoading ? 'loading loading-spinner' : ''}></span>
        解析并生成token
      </button>
    </div>
  </div>
  <div class="divider divider-info">备份/清理</div>
  <button class="btn btn-outline btn-accent">备份</button>
  <button class="btn btn-outline btn-accent">清理</button>
</div>

<dialog bind:this={dialogRef} class="modal" closedby="none">
  <div class="modal-box">
    <h3 class="text-lg font-bold">解析并生成成功</h3>
    <div class="flex flex-col gap-2 pt-4">
      <div class="flex items-center gap-2">
        <div class="w-12 text-right">Token:</div>
        <div class="flex-1 break-all select-all" title={tokenInfo.token}>
          {tokenInfo.token}
        </div>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-12 text-right">URL:</div>
        <div class="text-info flex-1 break-all underline select-all" title={tokenInfo.tenantUrl}>
          {tokenInfo.tenantUrl}
        </div>
      </div>
      <p class="mt-2 font-bold text-red-400">
        Token和URL已复制到剪切板, 是否前往账号管理页面保存此Token?
      </p>
    </div>
    <div class="modal-action">
      <button class="btn" onclick={closeDialog}>取消</button>
      <button class="btn btn-primary" onclick={goToAccount}>保存此token</button>
    </div>
  </div>
</dialog>
