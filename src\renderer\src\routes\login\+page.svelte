<script lang="ts">
  import { zodErrorToRecord } from '$lib/utils'
  import { z } from 'zod/v4'
  import toast from 'svelte-french-toast'
  import { apiLogin, apiGetUserInfo } from '$lib/apis'
  import { onMount } from 'svelte'
  import nprogress from 'nprogress'
  import rendererLogger from '$lib/utils/logger'

  const FormSchema = z.object({
    login: z.string({ error: '用户名不能为空' }).trim().min(4, { message: '用户名最少4位' }),
    password: z.string({ error: '密码不能为空' }).trim().min(6, { message: '密码最少6位' })
  })

  type FormData = Partial<z.infer<typeof FormSchema>>

  let { data = {} } = $props()

  let formData = $state<FormData>({
    login: data.username || '',
    password: data.password || ''
  })
  let loading = $state(false)
  let errorInfo = $state<Record<keyof FormData, string>>({})
  let otherConfig = $state({
    autoLogin: data.autoLogin || false,
    isRemember: data.isRemember || false
  })

  // 验证
  const validate = () => {
    const result = FormSchema.safeParse($state.snapshot(formData))
    if (!result.success) {
      const fieldErrors = zodErrorToRecord(result.error.message)
      errorInfo = fieldErrors
      return
    }
    errorInfo = {}
    return result.data
  }

  // 登录
  const handleSubmit = async () => {
    const validateData = validate()
    if (!validateData) return
    try {
      toast.loading('登录中...', { id: 'login' })
      loading = true
      const res = await apiLogin(validateData)
      rendererLogger.info('登录成功')
      toast.success('登录成功', { icon: '🎉' })
      await window.api.login.setRememberLoginInfo({
        isRemember: otherConfig.isRemember,
        autoLogin: otherConfig.autoLogin,
        username: otherConfig.isRemember ? validateData.login : '',
        password: otherConfig.isRemember ? validateData.password : ''
      })
      const token = res.data.token
      setTimeout(() => {
        window.api.login.toggleLoginState(token)
      }, 1000)
    } catch (error) {
      rendererLogger.error('登录失败', error.message)
      window.api.login.setRememberLoginInfo({
        isRemember: otherConfig.isRemember,
        autoLogin: false,
        username: otherConfig.isRemember ? validateData.login : '',
        password: otherConfig.isRemember ? validateData.password : ''
      })
    } finally {
      toast.remove('login')
      loading = false
    }
  }

  onMount(() => {
    async function init() {
      toast.loading('登录校验中...', { id: 'autoLogin' })
      loading = true
      try {
        await apiGetUserInfo()
        toast.success('登录成功', { icon: '🎉' })
        setTimeout(() => {
          window.api.login.toggleLoginState(data.token)
        }, 1000)
      } catch (error) {
        if (error.status === 401) {
          window.api.login.setRememberLoginInfo({
            autoLogin: false
          })
        }
      } finally {
        toast.remove('autoLogin')
        loading = false
      }
    }
    if (data.autoLogin && data.token) {
      rendererLogger.info('自动登录...')
      init()
    }
  })
</script>

<div class="h-full">
  <div class="flex flex-col gap-4 p-5">
    <div>
      <label class="label floating-label">
        <span>用户名</span>
        <input
          class="input input-md input-primary w-[70vw] {errorInfo.login && 'input-error'}"
          type="text"
          disabled={loading}
          bind:value={formData.login}
          autocomplete="off"
          placeholder="请输入用户名或邮箱"
          onblur={validate}
        />
      </label>
      <span class={{ 'validator-error': errorInfo.login }}>{errorInfo.login}</span>
    </div>
    <div>
      <label class="label floating-label">
        <span>密码</span>
        <input
          class="input input-md input-primary w-[70vw] {errorInfo.password && 'input-error'}"
          type="password"
          disabled={loading}
          bind:value={formData.password}
          autocomplete="off"
          placeholder="请输入密码"
          onblur={validate}
        />
      </label>
      <span class={{ 'validator-error': errorInfo.password }}>{errorInfo.password}</span>
    </div>
    <div>
      <label class="label">
        <input
          class="checkbox checkbox-primary"
          type="checkbox"
          bind:checked={otherConfig.autoLogin}
        />
        自动登录
      </label>
    </div>
    <div>
      <label class="label">
        <input
          class="checkbox checkbox-primary"
          type="checkbox"
          bind:checked={otherConfig.isRemember}
        />
        记住密码
      </label>
    </div>
    <button class="btn btn-primary" disabled={loading} onclick={handleSubmit}>登录</button>
  </div>
</div>
