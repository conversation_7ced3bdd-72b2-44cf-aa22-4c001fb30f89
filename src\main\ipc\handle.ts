import fs from 'fs-extra'
import { app, net, session, dialog } from 'electron'
import dayjs from 'dayjs'
import { randomBytes, randomUUID } from 'node:crypto'
import path from 'node:path'
import axios from 'axios'
import semver from 'semver'
import { database } from '@main/db'
import { getHash, toUrlSafeBase64 } from '@main/utils'
import { failure, success } from './response'
import { IDE, ThemeMode, type RememberLoginInfo } from '@types'
import FileStorage from '@main/services/FileStorage'
// import { PROTOCOL_NAME } from '@main/constant'
// import secrets from '@main/utils/secrets'
import {
  calculateDirectorySize,
  getCachePath,
  getChromePath,
  getCommandVersion,
  getExecutablePath,
  getIDEExtensionsPath,
  getIdeInstallDir,
  readJsonFile
} from '@main/utils/paths'
import { mainLogger } from '@main/utils/logger'
import aes from '@main/utils/aes'
import { windowService } from '@main/services/WindowService'
import { IpcChannel } from '@shared/IpcChannel'

const fileManager = new FileStorage()

export function aesEncrypt(text: string) {
  try {
    const obj = aes.encrypt(
      text,
      import.meta.env.VITE_SECRET_KEY as string,
      import.meta.env.VITE_SECRET_IV as string
    )
    return success(obj.encryptedData)
  } catch (error: any) {
    return failure(error.message)
  }
}

export function aesDecrypt(encryptedData: string) {
  try {
    const decryptedData = aes.decrypt(
      encryptedData,
      import.meta.env.VITE_SECRET_IV as string,
      import.meta.env.VITE_SECRET_KEY as string
    )
    return success(decryptedData)
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function getCacheSize() {
  const cachePath = getCachePath()
  mainLogger.info(`Calculating cache size for path: ${cachePath}`)
  try {
    const sizeInBytes = await calculateDirectorySize(cachePath)
    const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2)
    return success(sizeInMB)
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function clearCache() {
  const sessions = [session.defaultSession, session.fromPartition('persist:webview')]
  try {
    await Promise.all(
      sessions.map(async (session) => {
        await session.clearCache()
        await session.clearStorageData({
          storages: [
            'cookies',
            'filesystem',
            'shadercache',
            'websql',
            'serviceworkers',
            'cachestorage'
          ]
        })
      })
    )
    await fileManager.clearTemp()
    return success(true)
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function clearFiles() {
  try {
    await fileManager.clearFiles()
    return success(true)
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function clearBackups() {
  try {
    await fileManager.clearBackups()
    return success(true)
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function clearLogs() {
  try {
    await fileManager.clearLogs()
    return success(true)
  } catch (error: any) {
    return failure(error.message)
  }
}

export function getLoginState() {
  return !!getToken()
}

export function getToken() {
  // const encryptedToken = database.items.get('token')
  // if (!encryptedToken || !encryptedToken?.data) return ''
  // const token = secrets.decrypt(encryptedToken.data)
  // return token
  return database.items.get('token')
}

export function setToken(token?: string) {
  // const encryptedToken = secrets.encrypt(token)
  // database.items.set('token', encryptedToken)
  if (token) {
    database.items.set('token', token)
  } else {
    database.items.delete('token')
  }
}

export function getRememberLoginInfo() {
  const encryptedData = database.items.get('loginInfo')
  if (!encryptedData) return {}
  const res = aesDecrypt(encryptedData)
  if (!res.success) return {}
  return JSON.parse((res.data as string) || '{}')
}

export function setRememberLoginInfo(loginInfo: RememberLoginInfo) {
  const oldLoginInfo = getRememberLoginInfo()
  const res = aesEncrypt(
    JSON.stringify({
      ...oldLoginInfo,
      ...loginInfo
    })
  )
  database.items.set('loginInfo', res.data || '')
}

export function getTheme(): ThemeMode {
  return database.items.get('theme') || ThemeMode.system
}

export function setTheme(theme: ThemeMode) {
  database.items.set('theme', theme)
}

export function getIDE(): IDE {
  return database.items.get('ide') || IDE.VSCode
}

export function setIDE(ide: IDE) {
  try {
    if (!Object.values(IDE).includes(ide)) {
      return failure(ide + '不是有效的ide值,可选项为: ' + Object.values(IDE).join(','))
    }
    database.items.set('ide', ide)
    return success(ide, 'ide设置成功')
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function openPath(path: string) {
  try {
    await fileManager.openPath(path)
    return success(path)
  } catch (error: any) {
    mainLogger.error(`Failed to open path: ${path}`, error.message)
    return failure(error.message)
  }
}

/** 生成登录链接 */
export function generateLoginUrl() {
  try {
    const codeVerifier = toUrlSafeBase64(randomBytes(32))
    const codeChallenge = toUrlSafeBase64(getHash(Buffer.from(codeVerifier)))
    const state = randomUUID()
    const ide = getIDE()
    const protocol = ide === IDE.VSCode ? 'vscode' : 'cursor'
    const openUrl = `https://auth.augmentcode.com/authorize?response_type=code&code_challenge=${codeChallenge}&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=${protocol}://augment.vscode-augment/auth/result&state=${state}&scope=email&prompt=login`

    const oauthState = {
      /** 校验码(生成token时需要) */
      creationTime: dayjs().add(10, 'm').format('YYYY-MM-DD HH:mm:ss'),
      codeVerifier,
      codeChallenge,
      state
    }
    database.items.set('oauthState', oauthState)
    return success(openUrl)
  } catch (error: any) {
    return failure(error.message)
  }
}

/** 校验codeVerifier */
export async function verifyCode(code: string, tenantUrl: string, redirectUri: string) {
  try {
    const oauthState = await database.items.get('oauthState')
    if (!oauthState || !oauthState.codeVerifier) return failure('已过期')
    if (dayjs(oauthState.creationTime).isBefore(dayjs())) {
      database.items.delete('oauthState')
      return failure('已过期')
    }
    tenantUrl = tenantUrl.endsWith('/') ? tenantUrl : tenantUrl + '/'
    const res = await axios.post(`${tenantUrl}token`, {
      grant_type: 'authorization_code',
      client_id: 'augment-vscode-extension',
      code_verifier: oauthState.codeVerifier,
      redirect_uri: redirectUri,
      code: code
    })
    if (res.status !== 200) return failure(res.data?.error || '未知错误')
    const token = res.data?.access_token
    return success({ token })
  } catch (error: any) {
    return failure(error?.response?.data?.error || error.message || '未知错误')
  } finally {
    database.items.delete('oauthState')
  }
}

export async function getExtensionsInfo() {
  try {
    const extensionsPath = getIDEExtensionsPath()
    let extensionsList = readJsonFile(path.join(extensionsPath, 'extensions.json')) || []
    extensionsList = extensionsList.map((item: any) => ({
      extensionName: item.identifier?.id,
      version: item.version,
      extensionPath: item.location?.path
        ? path.normalize(item.location.path.replace(/^\//, '')).replace(/^c:/, 'C:')
        : null,
      relativeLocation: item.relativeLocation,
      installedDate: item.metadata?.installedTimestamp
        ? dayjs(item.metadata.installedTimestamp).format('YYYY-MM-DD HH:mm:ss')
        : null
    }))
    const augmentLocalInfo =
      extensionsList.find((item: any) => item.extensionName === 'augment.vscode-augment') || {}
    const res = await axios.get(
      'https://www.vscode-unpkg.net/_gallery/Augment/vscode-augment/latest'
    )
    const augmentRemoteInfo = res.data || {}
    const versions = (augmentRemoteInfo.versions || []).sort((a, b) =>
      semver.compare(a.version, b.version)
    )
    return success({
      extensionsList,
      augmentLocalInfo,
      augmentRemoteInfo: {
        ...augmentRemoteInfo,
        currentInfo: {
          ...versions[0],
          lastUpdated: dayjs(versions[0].lastUpdated).format('YYYY-MM-DD HH:mm:ss')
        },
        previewInfo: {
          ...versions[1],
          lastUpdated: dayjs(versions[1].lastUpdated).format('YYYY-MM-DD HH:mm:ss')
        }
      }
    })
  } catch (error: any) {
    return failure(error.message)
  }
}

export function getSoftwareInfo() {
  try {
    // 获取vscode目录
    const vscodeCommandPath = getExecutablePath('code')
    let vscodeRootPath: string | null = null
    if (vscodeCommandPath) {
      vscodeRootPath = getIdeInstallDir(vscodeCommandPath)
    } else {
      mainLogger.warn('getSoftwareInfo: vscode命令不存在')
    }
    // 获取cursor目录
    const cursorCommandPath = getExecutablePath('cursor')
    let cursorRootPath: string | null = null
    if (cursorCommandPath) {
      cursorRootPath = getIdeInstallDir(cursorCommandPath)
    } else {
      mainLogger.warn('getSoftwareInfo: cursor命令不存在')
    }
    const chromePath = getChromePath()
    const vscodeVersion = getCommandVersion('code')
    const cursorVersion = getCommandVersion('cursor')
    return success({
      vscodeRootPath,
      cursorRootPath,
      chromePath: chromePath ? path.dirname(chromePath) : null,
      vscodeVersion,
      cursorVersion
    })
  } catch (error: any) {
    return failure(error.message)
  }
}

export async function downloadFile(url: string, fileName?: string, openDialog: boolean = true) {
  if (!fileName) {
    fileName = url.split('/').pop() || 'download'
  }
  let fullPath = ''
  const window = windowService.getMainWindow()
  if (!window) {
    mainLogger.error('Download file failed: window not found')
    return
  }
  try {
    const fileExt = path.extname(fileName).replace('.', '')
    if (openDialog) {
      const res = await dialog.showOpenDialog(window!, {
        title: `选择文件夹以下载${fileExt || '文件'}`,
        defaultPath: app.getPath('downloads'),
        buttonLabel: '下载',
        properties: ['openDirectory']
      })
      if (res.canceled) {
        mainLogger.info('Download file canceled')
        window.webContents.send(IpcChannel.App_DownloadFileProgress, {
          fileName,
          progress: 0,
          ready: false,
          success: false,
          error: false,
          message: '下载已取消'
        })
        return
      }
      const selectedPath = res.filePaths[0]
      fullPath = path.join(selectedPath, fileName)
    } else {
      fullPath = path.join(app.getPath('downloads'), fileName)
    }
    mainLogger.info(`Downloading file from 【${url}】 to 【${fullPath}】`)
    // 发送开始下载消息, 打开下载进度窗口
    window.webContents.send(IpcChannel.App_DownloadFileProgress, {
      fileName,
      progress: 0,
      ready: true,
      success: false,
      error: false,
      message: '开始下载'
    })
    const request = net.request(url)
    // 请求超时处理（30秒超时）
    const requestTimeout = setTimeout(() => {
      request.abort() // 中止请求
      mainLogger.error('Download request timed out')
      window.webContents.send(IpcChannel.App_DownloadFileProgress, {
        fileName,
        progress: 0,
        ready: true,
        success: false,
        message: '请求超时'
      })
    }, 30000)
    request.on('error', (error) => {
      clearTimeout(requestTimeout)
      mainLogger.error('Download request failed:', error.message)
      window.webContents.send(IpcChannel.App_DownloadFileProgress, {
        fileName,
        progress: 0,
        ready: true,
        success: false,
        error: true,
        message: '网络连接失败'
      })
    })
    request.on('response', (response) => {
      clearTimeout(requestTimeout)
      // 获取文件总大小（可能不存在）
      const totalBytes = parseInt((response.headers['content-length'] as string) || '0', 10)
      let downloadedBytes = 0

      // 创建可写流
      const fileStream = fs.createWriteStream(fullPath)

      // 监听数据块，手动写入文件
      response.on('data', (chunk: Buffer) => {
        downloadedBytes += chunk.length
        fileStream.write(chunk) // 写入数据

        // 计算下载进度（如果知道文件总大小）
        if (totalBytes > 0) {
          const progress = Math.floor((downloadedBytes / totalBytes) * 100)
          window.webContents.send(IpcChannel.App_DownloadFileProgress, {
            fileName,
            progress,
            ready: true,
            success: false,
            error: false,
            message: '下载中'
          })
        }
      })

      // 下载完成
      response.on('end', () => {
        clearTimeout(requestTimeout)
        fileStream.end() // 关闭文件流
        console.log('Download completed!')
        mainLogger.info(`Download completed! 【${fullPath}】`)
        window.webContents.send(IpcChannel.App_DownloadFileProgress, {
          fileName,
          progress: 100,
          ready: true,
          success: true,
          error: false,
          message: '下载完成'
        })
      })

      // 错误处理
      response.on('error', (err: Error) => {
        clearTimeout(requestTimeout)
        fileStream.destroy() // 关闭流并删除不完整的文件
        fs.unlink(fullPath, (err) => {
          if (err) {
            mainLogger.error('Download failed delete file:', err.message)
          }
        }) // 尝试删除文件（可选）
        mainLogger.error('Download failed:', err.message)
        window.webContents.send(IpcChannel.App_DownloadFileProgress, {
          fileName,
          progress: 0,
          ready: true,
          success: false,
          error: true,
          message: '下载失败'
        })
      })
    })
    request.end()
  } catch (error: any) {
    mainLogger.error('Download file failed:', error.message)
    window.webContents.send(IpcChannel.App_DownloadFileProgress, {
      fileName,
      progress: 0,
      ready: true,
      success: false,
      error: true,
      message: '下载失败'
    })
  }
}
