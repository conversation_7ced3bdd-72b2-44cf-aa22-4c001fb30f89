import { mainLogger } from '@main/utils/logger'
import {
  getFilesPath,
  getTempPath,
  getBackupsPath,
  makeSureDirExists,
  clearDirectory,
  getLogsPath
} from '@main/utils/paths'
import dayjs from 'dayjs'
import { shell } from 'electron'
import path from 'path'

export default class FileStorage {
  private storageDir = getFilesPath()
  private tempDir = getTempPath()
  private backupsDir = getBackupsPath()
  private logsDir = getLogsPath()

  constructor() {
    makeSureDirExists(this.storageDir)
    makeSureDirExists(this.tempDir)
    makeSureDirExists(this.backupsDir)
  }

  public clearFiles = async (): Promise<void> => {
    await clearDirectory(this.storageDir)
  }

  public clearTemp = async (): Promise<void> => {
    await clearDirectory(this.tempDir)
  }

  public clearBackups = async (): Promise<void> => {
    await clearDirectory(this.backupsDir)
  }

  public clearLogs = async (): Promise<void> => {
    await clearDirectory(this.logsDir)
    makeSureDirExists(path.join(this.logsDir, dayjs().format('YYYY-MM-DD')))
  }

  public openPath = async (path: string): Promise<void> => {
    await shell.openPath(path)
  }
}
