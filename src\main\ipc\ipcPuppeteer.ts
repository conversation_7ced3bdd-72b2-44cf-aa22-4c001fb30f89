import { PuppeteerService } from '@main/services/PuppeteerService'
import { IpcChannel } from '@shared/IpcChannel'
import { ipcMain } from 'electron'
import { failure, success } from './response'
import { mainLogger } from '@main/utils/logger'

export function registerPuppeteerIpc() {
  ipcMain.handle(IpcChannel.App_GetChromeVersion, async () => {
    let pptr: PuppeteerService | null = null
    try {
      pptr = new PuppeteerService({
        headless: 'shell'
      })

      const page = await pptr.newPage()
      await page.goto('chrome://version/', { waitUntil: 'domcontentloaded' })

      const versionText = await page.$eval('#version #copy-content span:first-child', (el) =>
        el.textContent?.trim()
      )
      mainLogger.info(`Chrome version: ${versionText}`)
      return success(versionText)
    } catch (error: any) {
      mainLogger.error('Error getting Chrome version:', error)
      return failure(error.message)
    } finally {
      if (pptr) {
        await pptr.browserClose()
      }
    }
  })
}
