import { generatorDataGroup } from './utils'

export const platform = window.electron.process.platform

export const isMac = platform === 'darwin'
export const isWindows = platform === 'win32' || platform === 'win64'
export const isLinux = platform === 'linux'

export const menuList = [
  {
    name: '环境',
    icon: 'ant-design:code-outlined',
    path: '/main'
  },
  {
    name: '功能',
    icon: 'line-md:engine',
    path: '/main/functional'
  },
  {
    name: '账号',
    icon: 'line-md:person-search',
    path: '/main/accounts'
  },
  {
    name: '备份',
    icon: 'line-md:cloud-alt-upload',
    path: '/main/backup'
  },
  {
    name: '临时邮箱',
    icon: 'hugeicons:ai-mail',
    path: '/main/temp-mail'
  },
  {
    name: '设置',
    icon: 'line-md:cog-loop',
    bottom: true,
    path: '/main/settings'
  }
  // {
  //   name: '关于关于关于关于',
  //   icon: 'line-md:bell-loop',
  //   path: '/main/about'
  // }
]

export const AUGMENT_SESSIONS_KEY = `secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}`

export enum DialogTypeEnum {
  /** 添加 */
  Add = 'add',
  /** 编辑 */
  Edit = 'edit',
  /** 查看 */
  View = 'view'
}

export const AccountDialogTitleOption = generatorDataGroup(
  DialogTypeEnum.Add,
  '添加账号',
  DialogTypeEnum.Edit,
  '编辑账号',
  DialogTypeEnum.View,
  '查看账号'
)
