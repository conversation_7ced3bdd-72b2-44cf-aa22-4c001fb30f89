import path from 'node:path'
import fs from 'fs-extra'
import os from 'node:os'
import fsAsync from 'node:fs/promises'
import { app } from 'electron'
import * as chromeLauncher from 'chrome-launcher'
import { APP_NAME, APP_NAME_DEV, isDev, isWin } from '@main/constant'
import { IDE } from '@types'
import { getIDE } from '@main/ipc/handle'
import { mainLogger } from './logger'
import { execSync } from 'node:child_process'

/**
 * 确保目录存在
 * @param dir 目录路径
 */
export function makeSureDirExists(dir: string) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
}

/**
 * 删除目录下所有文件/目录
 * @param dirPath 目录路径
 */
export async function clearDirectory(dirPath: string) {
  try {
    const files = await fs.readdir(dirPath)
    await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(dirPath, file)
        await fs.remove(filePath)
      })
    )
  } catch (error) {
    mainLogger.error(`Failed to clear directory ${dirPath}:`, error)
    throw error
  }
}

/**
 * 获取资源路径
 */
export function getResourcePath() {
  return path.join(app.getAppPath(), 'resources')
}

/**
 * 获取数据路径
 */
export function getDataPath() {
  const userDataPath = getUserDataPath()
  const dataPath = path.join(userDataPath, 'Data')
  makeSureDirExists(dataPath)
  return dataPath
}

/**
 * 获取文件路径
 */
export function getFilesPath() {
  const userDataPath = getUserDataPath()
  return path.join(userDataPath, 'Data', 'Files')
}

/**
 * 获取备份路径
 */
export function getBackupsPath() {
  const userDataPath = getUserDataPath()
  return path.join(userDataPath, 'Data', 'Backups')
}

/**
 * 获取临时路径
 */
export function getTempPath() {
  return path.join(app.getPath('temp'), APP_NAME)
}

/**
 * 获取缓存路径
 */
export function getCachePath() {
  const userDataPath = getUserDataPath()
  return path.join(userDataPath, 'Cache')
}

/**
 * 获取日志路径
 */
export function getLogsPath() {
  const userDataPath = getUserDataPath()
  return path.join(userDataPath, 'logs')
}

/**
 * 获取指定项目用户数据目录
 * @param appName 项目名称 默认本应用名称
 * @returns 用户数据目录
 */
export function getUserDataPath(appName?: typeof APP_NAME | typeof APP_NAME_DEV | IDE) {
  if (!appName) {
    appName = isDev ? APP_NAME_DEV : APP_NAME
  }
  const userDataPath = path.join(app.getPath('appData'), appName)
  makeSureDirExists(userDataPath)
  return userDataPath
}

/**
 * 设置指定项目用户数据目录
 * @param appName 项目名称 默认本应用名称
 */
export function setUserDataPath(appName?: typeof APP_NAME | typeof APP_NAME_DEV | IDE) {
  if (!appName) {
    appName = isDev ? APP_NAME_DEV : APP_NAME
  }
  const userDataPath = getUserDataPath(appName)
  app.setPath('userData', userDataPath)
}

/**
 * 获取IDE storage路径
 */
export function getIDEStoragePath() {
  const ide = getIDE()
  return path.join(getUserDataPath(ide), 'User', 'globalStorage', 'storage.json')
}

/**
 * 获取IDE 数据库路径
 */
export function getIDEDatabasePath() {
  const ide = getIDE()
  const globalStoragePath = path.join(getUserDataPath(ide), 'User', 'globalStorage')
  makeSureDirExists(globalStoragePath)
  return path.join(globalStoragePath, 'state.vscdb')
}

/**
 * 获取IDE 工作区路径
 */
export function getIDEWorkspacePath() {
  const ide = getIDE()
  return path.join(getUserDataPath(ide), 'User', 'workspaceStorage')
}

/**
 * 获取ide扩展路径
 */
export function getIDEExtensionsPath() {
  const ide = getIDE()
  const extensionsPath = path.join(
    os.homedir(),
    ide === IDE.VSCode ? '.vscode' : '.cursor',
    'extensions'
  )
  return extensionsPath
}

/**
 * 获取指定扩展完整路径
 * @param extensionName 扩展名称 模糊匹配
 */
export function getFullExtensionPath(extensionName: string) {
  const extensionsPath = getIDEExtensionsPath()
  const extensions = fs.readdirSync(extensionsPath)
  const extension = extensions.find((item) => item.includes(extensionName))
  if (!extension) return null
  return path.join(extensionsPath, extension)
}

/**
 * 计算目录大小
 * @param directoryPath 目录路径
 * @returns 目录大小
 */
export async function calculateDirectorySize(directoryPath: string): Promise<number> {
  let totalSize = 0
  const items = await fsAsync.readdir(directoryPath)

  for (const item of items) {
    const itemPath = path.join(directoryPath, item)
    const stats = await fsAsync.stat(itemPath)

    if (stats.isFile()) {
      totalSize += stats.size
    } else if (stats.isDirectory()) {
      totalSize += await calculateDirectorySize(itemPath)
    }
  }
  return totalSize
}

/**
 * 读取json文件
 * @param filePath 文件路径
 * @returns 文件内容
 */
export function readJsonFile(filePath: string): any {
  return fs.readJSONSync(filePath, { throws: false, encoding: 'utf-8' })
}

/**
 * 写入json文件
 * @param filePath 文件路径
 * @param data 数据
 */
export function writeJsonFile(filePath: string, data: object) {
  fs.writeJSONSync(filePath, data, { encoding: 'utf-8', spaces: 4 })
}

/**
 * 获取可执行文件路径（跨平台兼容）
 * @param command 可执行文件名（如 code、cursor）
 * @returns 返回第一个找到的路径，找不到返回 null
 */
export function getExecutablePath(command: string): string | null {
  try {
    const cmd = isWin ? `where ${command}` : `which ${command}`
    const path = execSync(cmd, { stdio: 'pipe' }).toString().trim()
    return path.split('\n')[0] || null // 返回第一个匹配的路径
  } catch (err) {
    return null
  }
}

/**
 * 获取Chrome目录
 */
export function getChromePath(): string | null {
  try {
    const chromePath = chromeLauncher.getChromePath()
    // return path.dirname(chromePath)
    return chromePath
  } catch (error: any) {
    mainLogger.warn('getChromePath: Chrome不存在')
    return null
  }
}

/**
 * 获取IDE根目录
 * @param executablePath 可执行文件路径
 * @returns 根目录
 */
export function getIdeInstallDir(executablePath: string): string | null {
  if (!executablePath) return null
  const dir = path.dirname(executablePath)
  // 如果是vscode，返回上级目录, 如果是cursor，返回上级的上级的上级目录
  if (dir.endsWith('resources\\app\\bin')) {
    return path.dirname(path.dirname(path.dirname(dir)))
  } else if (dir.endsWith('bin')) {
    return path.dirname(dir)
  }
  return dir
}

/**
 * 获取命令版本号
 * @param commandName - 命令名称（如code、cursor）
 * @returns 返回版本号（如 "1.101.2"），失败返回 null
 */
export function getCommandVersion(commandName: string) {
  try {
    // 1. 执行命令获取原始版本信息（如 `code --version`）
    const rawOutput = execSync(`${commandName} --version`, {
      stdio: 'pipe',
      timeout: 3000 // 3秒超时防止卡死
    })
      .toString()
      .trim()

    // 2. 提取第一行作为版本号（VS Code/Cursor 的 --version 第一行均为正式版本号）
    const version = rawOutput.split('\n')[0]
    return version || null
  } catch (error: any) {
    mainLogger.warn(`获取 ${commandName} 版本号失败:`, error.message)
    return null
  }
}
