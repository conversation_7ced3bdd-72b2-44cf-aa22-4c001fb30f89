{"name": "ai-trial", "private": true, "version": "1.0.0", "author": "大咪", "description": "ai trial assistant", "main": "./out/main/index.js", "type": "module", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "axios": "^1.9.0", "better-sqlite3": "^12.2.0", "chrome-launcher": "^1.2.0", "connect-history-api-fallback": "^2.0.0", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "elecp-serve": "^1.0.3", "electron-log": "^5.4.0", "electron-store": "^8.2.0", "electron-window-state": "^5.0.3", "express": "^5.1.0", "fs-extra": "^11.3.0", "get-port": "^7.1.0", "mime": "^4.0.7", "node-schedule": "^2.1.1", "nprogress": "^0.2.0", "puppeteer-core": "^24.11.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "semver": "^7.7.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "svelte-french-toast": "^2.0.0-alpha.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/rebuild": "^4.0.1", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@iconify/svelte": "^5.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.21.1", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@types/better-sqlite3": "^7.6.13", "@types/connect-history-api-fallback": "^1.5.4", "@types/express": "^5.0.2", "@types/node": "^22.15.29", "@types/node-schedule": "^2.1.7", "@types/nprogress": "^0.2.3", "daisyui": "^5.0.43", "electron": "^36.3.2", "electron-builder": "^26.0.12", "electron-devtools-installer": "^4.0.0", "electron-vite": "^3.1.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.1", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "svelte": "^5.33.14", "svelte-check": "^4.2.1", "sveltekit-superforms": "^2.26.1", "tailwindcss": "^4.1.8", "tslib": "^2.8.1", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5", "zod": "^3.25.56"}}