import type { AugmentAccountRow, AugmentAccountInsert } from '@renderer/types'
import toast from 'svelte-french-toast'

interface AccountStore {
  accounts: AugmentAccountRow[]
}

export let accountStore = $state<AccountStore>({
  accounts: []
})

export async function getAccounts() {
  try {
    const res = await window.api.db.accounts.getAll()
    accountStore.accounts = res
    return res
  } catch (error) {
    toast.error('获取失败: ' + error.message)
    return []
  }
}

export async function getAccount(value: string | number, key: string = 'email') {
  try {
    const res = await window.api.db.accounts.get(value, key)
    return res
  } catch (error) {
    toast.error('获取失败: ' + error.message)
    return null
  }
}

export async function addAccount(account: AugmentAccountInsert) {
  try {
    await window.api.db.accounts.set(account)
    getAccounts()
    return true
  } catch (error) {
    toast.error('添加失败: ' + error.message)
    return false
  }
}

export async function updateAccount(account: Partial<AugmentAccountRow> & { id: number }) {
  try {
    await window.api.db.accounts.update(account)
    getAccounts()
    return true
  } catch (error) {
    toast.error('更新失败: ' + error.message)
    return false
  }
}

export async function deleteAccount(value: string | number, key: string = 'id') {
  try {
    const res = await window.api.db.accounts.delete(value, key)
    getAccounts()
    return res
  } catch (error) {
    toast.error('删除失败: ' + error.message)
    return false
  }
}

export async function batchSetAccounts(accounts: AugmentAccountInsert[]) {
  try {
    await window.api.db.accounts.batchSet(accounts)
    getAccounts()
    return true
  } catch (error) {
    toast.error('批量添加失败: ' + error.message)
    return false
  }
}

export async function clearAccounts() {
  try {
    await window.api.db.accounts.clear()
    getAccounts()
    return true
  } catch (error) {
    toast.error('清空失败: ' + error.message)
    return false
  }
}
