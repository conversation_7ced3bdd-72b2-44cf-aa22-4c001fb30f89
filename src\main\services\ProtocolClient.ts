import { isDev, PROTOCOL_NAME } from '@main/constant'
import { mainLogger } from '@main/utils/logger'
import path from 'path'

/** 注册自定义协议 */
export function registerProtocolClient(app: Electron.App) {
  if (isDev) {
    const appPath = process.execPath
    app.setAsDefaultProtocolClient(PROTOCOL_NAME, appPath, [
      path.resolve(process.argv[1]), // 主脚本路径
      '--'
    ])
  } else {
    if (process.defaultApp) {
      if (process.argv.length >= 2) {
        app.setAsDefaultProtocolClient(PROTOCOL_NAME, process.execPath, [process.argv[1]])
      }
    }
    app.setAsDefaultProtocolClient(PROTOCOL_NAME)
  }
  mainLogger.info('自定义协议注册完成')
}

/**
 * 解析协议参数
 */
export function parseProtocolParams(url: string) {
  if (!url) return
  const urlObj = new URL(url)
  const host = urlObj.host
  mainLogger.debug('host====', host)
  const pathname = urlObj.pathname
  mainLogger.debug('pathname====', pathname)
  if (host === 'augment.vscode-augment' && pathname === '/auth/result') {
    const code = urlObj.searchParams.get('code')
    const state = urlObj.searchParams.get('state')
    const tenantUrl = urlObj.searchParams.get('tenant_url')
    mainLogger.debug('code====', code)
    mainLogger.debug('state====', state)
    mainLogger.debug('tenantUrl====', tenantUrl)
  }
}
