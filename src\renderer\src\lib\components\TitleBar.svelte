<script lang="ts">
  import type { Snippet } from 'svelte'
  import logoImg from '@assets/images/logo.png'
  interface Props {
    logoUrl?: string
    children?: Snippet
    avatar?: Snippet
  }
  const { logoUrl, children, avatar }: Props = $props()
</script>

<nav class="flex-shrink-0">
  <div class="drag bg-base-300 nav-h flex items-center justify-between pr-[var(--navbar-pr,96px)]">
    <div class="flex flex-1 items-center gap-2 px-2">
      <img
        class="h-6 w-6 cursor-pointer overflow-hidden object-cover"
        src={logoUrl || logoImg}
        alt="app-logo"
      />
      {#if children}
        <div class="flex-1">{@render children()}</div>
      {:else}
        <div class="flex-1">Augment小助手</div>
      {/if}
    </div>
    <div class="no-drag flex items-center justify-center">
      {@render avatar?.()}
    </div>
  </div>
</nav>
