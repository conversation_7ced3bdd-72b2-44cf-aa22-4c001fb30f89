{
  // "extends": "./.svelte-kit/tsconfig.json",
  "extends": "./.svelte-kit/tsconfig.json",
  "include": [
    "src/preload/**/*.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.svelte",
    "packages/shared/**/*"
  ],
  "compilerOptions": {
    "composite": true,
    "target": "esnext",
    "module": "esnext",
    "sourceMap": false,
    "strict": false,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "noImplicitReturns": true,
    "allowJs": true,
    "checkJs": true,
    "moduleResolution": "bundler",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": ".",
    "paths": {
      "$lib/*": [
        "src/renderer/src/lib/*"
      ],
      "@types": [
        "src/renderer/src/types/index.ts"
      ],
      "@renderer/*": [
        "src/renderer/src/*"
      ],
      "@shared/*": [
        "packages/shared/*"
      ],
      "@assets/*": [
        "src/renderer/src/assets/*"
      ]
    }
  }
}