import { sqlLogger } from '@main/utils/logger'
import { SQLiteConnection } from '../connection'
import { IDE } from '@types'

interface ItemRow {
  key: string
  value: string
}

export class ItemTable {
  private connection: SQLiteConnection
  private defaults = new Map<string, any>([
    ['ide', IDE.VSCode],
    ['theme', 'system']
    // ['loginState', false]
  ])
  constructor(connection: SQLiteConnection, initDatabase: boolean = false) {
    this.connection = connection
    if (initDatabase) {
      this.init()
      this.initDefaultValues()
    }
  }

  private init() {
    this.connection.db.exec(`
      CREATE TABLE IF NOT EXISTS ItemTable (
        key TEXT UNIQUE ON CONFLICT REPLACE,
        value BLOB
      )
    `)
  }

  private initDefaultValues() {
    const missingDefaults = new Map<string, any>()
    this.defaults.forEach((defaultValue, key) => {
      if (this.get(key) === null) {
        missingDefaults.set(key, defaultValue)
      }
    })
    if (missingDefaults.size > 0) {
      this.batchSet(missingDefaults)
    }
  }

  public get(key: string): any {
    const stmt = this.connection.db.prepare('SELECT value FROM ItemTable WHERE key = ?')
    const row = stmt.get(key) as ItemRow
    return row ? this.parseValue(row.value) : null
  }

  public getAll(): Map<string, any> {
    const result = new Map<string, any>()
    const stmt = this.connection.db.prepare('SELECT key, value FROM ItemTable')
    const rows = stmt.all() as ItemRow[]

    rows.forEach((row) => {
      result.set(row.key, this.parseValue(row.value))
    })

    return result
  }

  public set(key: string, value: any): void {
    const stmt = this.connection.db.prepare(`
      INSERT INTO ItemTable (key, value)
      VALUES (?, ?)
    `)
    stmt.run(key, this.serializeValue(value))
  }

  public delete(key: string): boolean {
    const stmt = this.connection.db.prepare('DELETE FROM ItemTable WHERE key = ?')
    const result = stmt.run(key)
    return result.changes > 0
  }

  public batchSet(items: Map<string, any>): void {
    this.connection.transaction(() => {
      const stmt = this.connection.db.prepare(`
                INSERT INTO ItemTable (key, value)
                VALUES (?, ?)
                ON CONFLICT(key) DO UPDATE SET value = excluded.value
            `)

      items.forEach((value, key) => {
        stmt.run(key, this.serializeValue(value))
      })
    })
  }

  private serializeValue(value: any): string {
    try {
      return typeof value === 'string' ? value : JSON.stringify(value)
    } catch (error: any) {
      sqlLogger.warn('serializeValue warn: ', error.message, value)
      return String(value)
    }
  }

  private parseValue(value: any): any {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    }
    return value
  }
}
