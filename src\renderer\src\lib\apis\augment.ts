import axios from 'axios'
import dayjs from 'dayjs'
import { isEmpty } from '$lib/utils'

export const apiGetAugmentSubscriptionInfo = (baseUrl: string, token: string) => {
  const url = baseUrl.endsWith('/') ? `${baseUrl}subscription-info` : `${baseUrl}/subscription-info`
  return new Promise((resolve, reject) => {
    axios({
      url,
      method: 'POST',
      data: {},
      headers: {
        Authorization: `Bearer ${token}`
        // 'User-Agent': 'Augment.vscode-augment/0.509.1 (win32; x64; 10.0.26100) vscode/1.102.1'
      }
    })
      .then((res) => {
        const data = {
          end_date: null,
          usage_balance_depleted: 1
        }
        if (res.data.subscription?.ActiveSubscription) {
          const { usage_balance_depleted, end_date } = res.data.subscription.ActiveSubscription
          data.usage_balance_depleted = isEmpty(usage_balance_depleted)
            ? 1
            : +usage_balance_depleted
          data.end_date = end_date ? dayjs(end_date).format('YYYY-MM-DD HH:mm:ss') : null
        }
        resolve(data)
      })
      .catch((err) => {
        reject(err.response.data || '未知错误')
      })
  })
}
