<script lang="ts">
  import Icon from '@iconify/svelte'
  import { getContext, onMount } from 'svelte'
  import semver from 'semver'
  import chromeIcon from '@assets/images/chrome.png'
  import vscodeIcon from '@assets/images/vscode.png'
  import cursorIcon from '@assets/images/cursor.png'
  import augmentIcon from '@assets/images/augment.png'
  import type { Attachment } from 'svelte/attachments'
  import { envInfo, settings } from '$lib/stores/global.svelte'
  import toast from 'svelte-french-toast'
  import { IDE } from '@renderer/types'
  import Loading from '$lib/components/Loading.svelte'

  let clientWidth = $state(0)
  let ideProtocol = $derived(
    settings.ide === IDE.VSCode ? 'vscode:extension/' : 'cursor:extension/'
  )

  // 动态提示
  function setDataTip(content: string): Attachment {
    return (node) => {
      const textElement = node.querySelector('.desc')
      if (clientWidth && textElement && textElement.scrollWidth > textElement.clientWidth) {
        node.setAttribute('data-tip', content)
      } else {
        node.removeAttribute('data-tip')
      }
    }
  }

  const getDownloadAugmentFileName = (version: string) => {
    return envInfo.augmentLocalInfo.extensionName + '-' + version + '.vsix'
  }

  // onMount(async () => {})
</script>

<div
  class="relative grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-2 lg:grid-cols-2"
  bind:clientWidth
>
  <ul class="list-box">
    <li class="border-info/50 border-b p-4 pb-2 font-bold tracking-wide opacity-60">Common</li>
    <li class="list-row items-center">
      <div>
        <img class="size-12" src={chromeIcon} alt="chrome icon" />
      </div>
      <div class="list-col-grow flex min-w-0 flex-col gap-1 text-xs">
        <div class="flex items-center">
          <span class="flex-shrink-0">路径：</span>
          <button
            class="hover:text-info tooltip tooltip-top w-fit min-w-0 cursor-pointer"
            data-tip="打开文件夹"
            onclick={async () => {
              const res = await window.api.openPath(envInfo.softwareInfo.chromePath)
              if (!res.success) {
                toast.error(res.message)
              }
            }}
          >
            <span class="inline-block max-w-full truncate">{envInfo.softwareInfo.chromePath}</span>
          </button>
        </div>
        <div
          class="tooltip tooltip-top tooltip-info flex items-center"
          {@attach setDataTip(envInfo.chromeVersion)}
        >
          <span class="desc inline-block max-w-full truncate">版本号：{envInfo.chromeVersion}</span>
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="启动"
      >
        <Icon icon="iconoir:play" width="18" height="18" />
      </button>
    </li>
    <li class="list-row items-center">
      <div>
        <img class="size-12" src={vscodeIcon} alt="vscode icon" />
      </div>
      <div class="list-col-grow flex min-w-0 flex-col gap-1 text-xs">
        <div class="flex items-center">
          <span class="flex-shrink-0">路径：</span>
          <button
            class="hover:text-info tooltip tooltip-top w-fit min-w-0 cursor-pointer"
            data-tip="打开文件夹"
            onclick={async () => {
              const res = await window.api.openPath(envInfo.softwareInfo.vscodeRootPath)
              if (!res.success) {
                toast.error(res.message)
              }
            }}
          >
            <span class="inline-block max-w-full truncate"
              >{envInfo.softwareInfo.vscodeRootPath}</span
            >
          </button>
        </div>
        <div>
          <span>版本号：</span>
          <span>{envInfo.softwareInfo.vscodeVersion}</span>
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="启动"
      >
        <Icon icon="iconoir:play" width="18" height="18" />
      </button>
    </li>
    <li class="list-row items-center">
      <div>
        <img class="size-12" src={cursorIcon} alt="cursor icon" />
      </div>
      <div class="list-col-grow flex min-w-0 flex-col gap-1 text-xs">
        <div class="flex items-center">
          <span class="flex-shrink-0">路径：</span>
          <button
            class="hover:text-info tooltip tooltip-top w-fit min-w-0 cursor-pointer"
            data-tip="打开文件夹"
            onclick={async () => {
              const res = await window.api.openPath(envInfo.softwareInfo.cursorRootPath)
              if (!res.success) {
                toast.error(res.message)
              }
            }}
          >
            <span class="inline-block max-w-full truncate"
              >{envInfo.softwareInfo.cursorRootPath}</span
            >
          </button>
        </div>
        <div>
          <span>版本号：</span>
          <span>{envInfo.softwareInfo.cursorVersion}</span>
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="启动"
      >
        <Icon icon="iconoir:play" width="18" height="18" />
      </button>
    </li>
  </ul>
  <ul class="list-box">
    <li class="border-info/50 border-b p-4 pb-2 font-bold tracking-wide opacity-60">Augment</li>
    <li class="list-row items-center">
      <div class="avatar">
        <div class="mask mask-heart size-12">
          <img src={augmentIcon} alt="augment icon" />
        </div>
      </div>
      <div class="list-col-grow relative flex min-w-0 flex-col gap-1 text-xs">
        <div class="badge badge-accent badge-xs absolute -top-3 -left-10 rounded-bl-none">本地</div>
        <div
          class="tooltip tooltip-top tooltip-info flex items-center"
          {@attach setDataTip(envInfo.augmentRemoteInfo.shortDescription)}
        >
          <span class="desc inline-block max-w-full truncate"
            >{envInfo.augmentRemoteInfo.shortDescription}</span
          >
        </div>
        <div class="flex items-center gap-1">
          <span>版本号：{envInfo.augmentLocalInfo.version}</span>
          {#if envInfo.augmentLocalInfo.version && envInfo.augmentCurrentInfo.version && semver.lt(envInfo.augmentLocalInfo.version, envInfo.augmentCurrentInfo.version)}
            <div class="badge badge-soft badge-warning badge-xs">版本落后</div>
          {/if}
        </div>
        <div
          class="tooltip tooltip-top tooltip-info flex items-center"
          {@attach setDataTip(envInfo.augmentLocalInfo.installedDate)}
        >
          <span class="desc inline-block max-w-full truncate"
            >安装时间：{envInfo.augmentLocalInfo.installedDate}</span
          >
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="打开"
        onclick={() => {
          window.api.openWebsite(ideProtocol + envInfo.augmentLocalInfo.extensionName)
        }}
      >
        <Icon icon="fluent:open-16-regular" width="18" height="18" />
      </button>
    </li>
    <li class="list-row items-center">
      <div class="avatar avatar-placeholder">
        <div class="bg-info/20 mask mask-squircle size-12">
          <span class="text-xs">正式版</span>
        </div>
      </div>
      <div class="list-col-grow flex min-w-0 flex-col gap-1 text-xs">
        <div
          class="tooltip tooltip-top tooltip-info flex items-center"
          {@attach setDataTip(envInfo.augmentCurrentInfo.lastUpdated)}
        >
          <span class="desc inline-block max-w-full truncate"
            >更新时间：{envInfo.augmentCurrentInfo.lastUpdated}</span
          >
        </div>
        <div>
          <span>版本号：{envInfo.augmentCurrentInfo.version}</span>
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="下载"
        onclick={() => {
          const downloadUrl = (envInfo.augmentCurrentInfo.files || []).find(
            (file) => file.assetType === 'Microsoft.VisualStudio.Services.VSIXPackage'
          )?.source
          console.log('downloadUrl', downloadUrl)
          window.api.downloadFile(
            downloadUrl,
            getDownloadAugmentFileName(envInfo.augmentCurrentInfo.version)
          )
        }}
      >
        <Icon icon="icon-park-outline:download-one" width="18" height="18" />
      </button>
    </li>
    <li class="list-row items-center">
      <div class="avatar avatar-placeholder">
        <div class="bg-secondary/20 mask mask-hexagon-2 size-12">
          <span class="text-xs">预览版</span>
        </div>
      </div>
      <div class="list-col-grow flex min-w-0 flex-col gap-1 text-xs">
        <div
          class="tooltip tooltip-top tooltip-info flex items-center"
          {@attach setDataTip(envInfo.augmentPreviewInfo.lastUpdated)}
        >
          <span class="desc inline-block max-w-full truncate"
            >更新时间：{envInfo.augmentPreviewInfo.lastUpdated}</span
          >
        </div>
        <div>
          <span>版本号：{envInfo.augmentPreviewInfo.version}</span>
        </div>
      </div>
      <button
        class="btn btn-square btn-outline btn-info btn-sm tooltip tooltip-top tooltip-info"
        data-tip="下载"
        onclick={() => {
          const downloadUrl = (envInfo.augmentPreviewInfo.files || []).find(
            (file) => file.assetType === 'Microsoft.VisualStudio.Services.VSIXPackage'
          )?.source
          console.log('downloadUrl', downloadUrl)
          window.api.downloadFile(
            downloadUrl,
            getDownloadAugmentFileName(envInfo.augmentPreviewInfo.version)
          )
        }}
      >
        <Icon icon="icon-park-outline:download-one" width="18" height="18" />
      </button>
    </li>
  </ul>
  <Loading loading={envInfo.loading} />
</div>

<style lang="postcss">
  @reference '@assets/styles/index.css';
  .list-box {
    @apply list bg-info/10 rounded-box w-full shadow-sm transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md;
  }
</style>
