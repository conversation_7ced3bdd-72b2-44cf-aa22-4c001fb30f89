<script lang="ts">
  import { apiGetUserInfo, apiLogout } from '$lib/apis'
  import type { UserData } from '$lib/apis/type'
  import Menu from '$lib/components/Menu.svelte'
  import TitleBar from '$lib/components/TitleBar.svelte'
  import { menuList } from '$lib/constant'
  import Icon from '@iconify/svelte'
  import nProgress from 'nprogress'
  import { onMount, setContext } from 'svelte'
  import avatarErrImg from '@assets/images/avatar_err.png'
  import toast from 'svelte-french-toast'
  import BackToTop from '$lib/components/BackToTop.svelte'
  import { getAugmentInfo, getSoftwareInfo, initSettings } from '$lib/stores/global.svelte'
  import DownloadProgress from '$lib/components/DownloadProgress.svelte'

  nProgress.configure({
    parent: '#main',
    showSpinner: false
  })

  let { children } = $props()
  let collapsed = $state(false)
  let userInfo = $state<UserData>({})
  let dialogRef = $state<HTMLDialogElement | null>(null)
  let mainRef = $state<HTMLDivElement>()

  async function logout() {
    await apiLogout()
    await window.api.login.toggleLoginState()
  }

  async function getUserInfo() {
    const res = await apiGetUserInfo()
    userInfo = res.data || {}
  }

  function editUserInfo(event: MouseEvent) {
    event.target.blur()
    dialogRef.showModal()
  }

  setContext('userInfo', () => userInfo)

  onMount(() => {
    initSettings()
    getUserInfo()
    const timer = setInterval(getUserInfo, 1000 * 60 * 10)
    getSoftwareInfo()
    getAugmentInfo()
    return () => {
      clearInterval(timer)
    }
  })
</script>

{#snippet avatar()}
  <div class="dropdown dropdown-center cursor-pointer">
    <div class="flex items-center" tabindex="0" role="button">
      <div class="tooltip tooltip-left tooltip-info">
        {#if userInfo.nickname}
          <div class="tooltip-content">
            <div class="-rotate-10 animate-bounce">{userInfo.nickname}</div>
          </div>
        {/if}
        <div class="avatar avatar-online">
          <div class="w-8 rounded-full">
            <img src={userInfo.avatar || avatarErrImg} alt="avatar" />
          </div>
        </div>
      </div>
    </div>
    <ul class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 shadow-sm">
      <li><button onclick={editUserInfo}>修改资料</button></li>
      <!-- <li><button onclick={(e) => e.target.blur()}>升级</button></li> -->
      <li>
        <button
          onclick={(e) => {
            logout(e)
            e.target.blur()
          }}>退出</button
        >
      </li>
    </ul>
  </div>
{/snippet}

<TitleBar --navbar-pr="150px" {avatar} />

<div class="relative flex min-h-0 flex-1 overflow-hidden">
  <Menu bind:collapsed {menuList} />
  <main id="main" bind:this={mainRef} class="h-full flex-1 overflow-y-auto p-2 select-auto">
    {@render children()}
  </main>
  <BackToTop
    class="tooltip tooltip-left tooltip-info"
    tip="返回顶部"
    scrollElement={mainRef}
    --backtotop-btn-size="32px"
    --backtotop-btn-position="24px"
  />
</div>

<dialog bind:this={dialogRef} class="modal">
  <div class="modal-box">
    <h3 class="text-lg font-bold">Hello!</h3>
    <p class="py-4">Press ESC key or click outside to close</p>
  </div>
  <form method="dialog" class="modal-backdrop">
    <button>close</button>
  </form>
</dialog>

<DownloadProgress />

<style>
  main {
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
