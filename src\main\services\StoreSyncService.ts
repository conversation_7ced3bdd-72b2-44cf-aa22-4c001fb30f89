import { IpcChannel } from '@shared/IpcChannel'
import type { StoreSyncAction } from '@types'
import { BrowserWindow, ipcMain } from 'electron'

/**
 * StoreSyncService 类管理主进程中多个窗口之间的 Redux 存储同步。
 * 它使用单例模式来确保应用程序中只存在一个同步服务实例.
 *
 * 主要特征：
 * 1. 管理窗口的存储同步订阅
 * 2. 处理窗口之间的存储同步的 IPC 通信
 * 3. 从一个窗口广播 Redux 动作到所有其他窗口
 * 4. 为同步的动作添加元数据，以防止无限同步循环
 */
export class StoreSyncService {
  private static instance: StoreSyncService
  private windowIds: number[] = []
  private isIpcHandlerRegistered = false

  private constructor() {
    return
  }

  /**
   * 获取 StoreSyncService 的单例实例
   */
  public static getInstance(): StoreSyncService {
    if (!StoreSyncService.instance) {
      StoreSyncService.instance = new StoreSyncService()
    }
    return StoreSyncService.instance
  }

  /**
   * 订阅一个窗口到存储同步
   * @param windowId 要订阅的窗口ID
   */
  public subscribe(windowId: number): void {
    if (!this.windowIds.includes(windowId)) {
      this.windowIds.push(windowId)
    }
  }

  /**
   * 取消订阅一个窗口到存储同步
   * @param windowId 要取消订阅的窗口ID
   */
  public unsubscribe(windowId: number): void {
    this.windowIds = this.windowIds.filter((id) => id !== windowId)
  }

  /**
   * 注册存储同步通信的 IPC 处理程序
   * 处理窗口订阅、取消订阅和动作广播
   */
  public registerIpcHandler(): void {
    if (this.isIpcHandlerRegistered) return

    // 订阅
    ipcMain.handle(IpcChannel.StoreSync_Subscribe, (event) => {
      const windowId = BrowserWindow.fromWebContents(event.sender)?.id
      if (windowId) {
        this.subscribe(windowId)
      }
    })
    // 取消订阅
    ipcMain.handle(IpcChannel.StoreSync_Unsubscribe, (event) => {
      const windowId = BrowserWindow.fromWebContents(event.sender)?.id
      if (windowId) {
        this.unsubscribe(windowId)
      }
    })
    // 更新
    ipcMain.handle(IpcChannel.StoreSync_OnUpdate, (event, action: StoreSyncAction) => {
      const sourceWindowId = BrowserWindow.fromWebContents(event.sender)?.id

      if (!sourceWindowId) return

      // 广播动作到所有其他窗口
      this.broadcastToOtherWindows(sourceWindowId, action)
    })

    this.isIpcHandlerRegistered = true
  }

  /**
   * 广播一个 Redux 动作到所有其他窗口，除了源窗口
   * @param sourceWindowId 源窗口的ID
   * @param action 要广播的 Redux 动作
   */
  private broadcastToOtherWindows(sourceWindowId: number, action: StoreSyncAction): void {
    // 添加元数据以指示此动作来自同步
    const syncAction = {
      ...action,
      meta: {
        ...action.meta,
        fromSync: true,
        source: `windowId:${sourceWindowId}`
      }
    }

    // 发送给所有其他窗口，除了源窗口
    this.windowIds.forEach((windowId) => {
      if (windowId !== sourceWindowId) {
        const targetWindow = BrowserWindow.fromId(windowId)
        if (targetWindow && !targetWindow.isDestroyed()) {
          targetWindow.webContents.send(IpcChannel.StoreSync_BroadcastSync, syncAction)
        } else {
          this.unsubscribe(windowId)
        }
      }
    })
  }
}

// 导出单例实例
const storeSyncService = StoreSyncService.getInstance()
export default storeSyncService
