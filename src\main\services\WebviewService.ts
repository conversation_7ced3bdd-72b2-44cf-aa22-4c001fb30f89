import { session, shell, webContents } from 'electron'

/**
 * 初始化WebView会话的用户代理从用户代理中移除FreeAg和Electron
 */
export function initSessionUserAgent() {
  const wvSession = session.fromPartition('persist:webview')
  const originUA = wvSession.getUserAgent()
  const newUA = originUA.replace(/FreeAg\/\S+\s/, '').replace(/Electron\/\S+\s/, '')

  wvSession.setUserAgent(newUA)
}

/**
 * WebviewService 处理自 webview 元素打开的链接的行为。它控制链接是否应该在应用程序内打开或在外部浏览器中打开。
 */
export function setOpenLinkExternal(webviewId: number, isExternal: boolean) {
  const webview = webContents.fromId(webviewId)
  if (!webview) return

  webview.setWindowOpenHandler(({ url }) => {
    if (isExternal) {
      shell.openExternal(url)
      return { action: 'deny' }
    } else {
      return { action: 'allow' }
    }
  })
}
