import request from '$lib/utils/request'
import type { LoginFormData, LoginResponseData, UserInfoResponseData } from './type'

/** 登录 */
export const apiLogin = (data: LoginFormData): Promise<LoginResponseData> =>
  request.post('/auth/sign_in', data)

/** 获取用户信息 */
export const apiGetUserInfo = (): Promise<UserInfoResponseData> => request.get('/users/info')

/** 退出 */
export const apiLogout = (): Promise<void> =>
  request.get('/users/sign_out', {
    headers: {
      'hide-toast': true
    }
  })
