<script lang="ts">
  import Icon from '@iconify/svelte'
  import type { DownloadProgressMessage } from '@renderer/types'
  import { IpcChannel } from '@shared/IpcChannel'
  import { onMount } from 'svelte'
  import toast from 'svelte-french-toast'
  import { cubicOut } from 'svelte/easing'
  import { Tween } from 'svelte/motion'

  let dialogRef = $state<HTMLDialogElement | null>(null)
  let downloadProgress = $state<DownloadProgressMessage>({
    fileName: '',
    progress: 0,
    ready: false,
    success: false,
    message: ''
  })
  let progressValue = new Tween(0, {
    duration: 400,
    easing: cubicOut
  })
  let showCloseIcon = $derived(downloadProgress.error || downloadProgress.success)
  let progressText = $derived(downloadProgress.ready ? downloadProgress.message : '')

  function openDialog() {
    if (!dialogRef.open) {
      dialogRef.showModal()
    }
  }

  function closeDialog() {
    if (!dialogRef.open) {
      return
    }
    dialogRef.close()
    downloadProgress = {
      fileName: '',
      progress: 0,
      ready: false,
      success: false,
      message: ''
    }
  }

  function handleDownloadProgress(_, data: DownloadProgressMessage) {
    downloadProgress = data
    if (data.ready && data.progress === 0) {
      openDialog()
    } else if (!data.ready) {
      toast.error(data.message, { icon: '🚫' })
    } else if (data.success) {
      toast.success(data.message, { icon: '🎉' })
    } else if (data.error) {
      toast.error(data.message, { icon: '💥' })
    }
  }

  $effect(() => {
    progressValue.target = downloadProgress.progress
  })

  onMount(() => {
    window.electron.ipcRenderer.on(IpcChannel.App_DownloadFileProgress, handleDownloadProgress)

    return () => {
      window.electron.ipcRenderer.removeListener(
        IpcChannel.App_DownloadFileProgress,
        handleDownloadProgress
      )
    }
  })
</script>

<dialog bind:this={dialogRef} class="modal" closedby="none">
  <div class="modal-box">
    {#if showCloseIcon}
      <form method="dialog">
        <button class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2" onclick={closeDialog}
          >✕</button
        >
      </form>
    {/if}
    <h3 class="text-lg font-bold">
      {downloadProgress.fileName ? `下载文件 (${downloadProgress.fileName})` : '下载文件'}
    </h3>
    <div class="pt-4">
      <div class="flex items-center gap-2">
        <progress
          class="progress {downloadProgress.success
            ? 'progress-success'
            : downloadProgress.error
              ? 'progress-error'
              : 'progress-info'} flex-1"
          value={progressValue.current}
          max="100"
        ></progress>
        <p class="w-10 text-sm text-gray-500">{downloadProgress.progress}%</p>
      </div>
      {#if downloadProgress.ready}
        <div
          class="mt-2 flex items-center justify-center gap-1 text-center text-sm {downloadProgress.success
            ? 'text-success'
            : downloadProgress.error
              ? 'text-error'
              : 'text-gray-500'}"
        >
          <span>{progressText}</span>
          {#if downloadProgress.success}
            <Icon icon="line-md:confirm-circle" width="22" height="22" />
          {:else if downloadProgress.error}
            <Icon icon="line-md:close-circle" width="22" height="22" />
          {:else if downloadProgress.message === '下载中'}
            <span class="loading loading-dots loading-sm"></span>
          {/if}
        </div>
      {/if}
    </div>
  </div>
  {#if showCloseIcon}
    <form method="dialog" class="modal-backdrop">
      <button onclick={closeDialog}>close</button>
    </form>
  {/if}
</dialog>
