import { app, BrowserWindow, nativeTheme } from 'electron'
import icon from '../../../build/icon.png?asset'
import { isLinux, isMac } from '@main/constant'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'
import electronServe from 'elecp-serve'
import { titleBarOverlayDark, titleBarOverlayLight } from '@main/constant'
import { initSessionUserAgent } from './WebviewService'
import { mainLogger } from '@main/utils/logger'

class LoginService {
  private static instance: LoginService | null = null
  private loginWindow: BrowserWindow | null = null
  private lastRendererProcessCrashTime: number = 0

  public static getInstance(): LoginService {
    if (!LoginService.instance) {
      LoginService.instance = new LoginService()
    }
    return LoginService.instance
  }

  public createLoginWindow(): BrowserWindow {
    mainLogger.info('创建登录窗口')
    if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      this.loginWindow.show()
      this.loginWindow.focus()
      return this.loginWindow
    }

    this.loginWindow = new BrowserWindow({
      title: 'Augment小助手',
      width: 400,
      height: 600,
      resizable: false,
      show: false,
      transparent: isMac,
      vibrancy: 'sidebar',
      visualEffectState: 'active',
      titleBarStyle: 'hidden',
      frame: false,
      titleBarOverlay: nativeTheme.shouldUseDarkColors ? titleBarOverlayDark : titleBarOverlayLight,
      maximizable: false,
      fullscreenable: false,
      autoHideMenuBar: true,
      darkTheme: nativeTheme.shouldUseDarkColors,
      trafficLightPosition: { x: 8, y: 12 },
      ...(isLinux ? { icon } : {}),
      webPreferences: {
        preload: join(__dirname, '../preload/index.mjs'),
        sandbox: false,
        webSecurity: false, // 允许加载本地资源
        webviewTag: true,
        allowRunningInsecureContent: true, // 允许 HTTPS 页面从 HTTP URL 运行 JavaScript、CSS 或插件
        backgroundThrottling: false // 禁用背景处理
      }
    })

    this.setupLoginWindow(this.loginWindow)

    //初始化MinApp网页视图的用户代理
    initSessionUserAgent()
    // if (is.dev) {
    //   this.loginWindow.webContents.openDevTools()
    // }
    return this.loginWindow
  }

  private setupLoginWindow(loginWindow: BrowserWindow) {
    // 窗口事件
    this.setupWindowEvents(loginWindow)
    // 窗口close closed
    this.setupWindowLifecycleEvents(loginWindow)
    // 窗口崩溃/卡死行为
    this.setupLoginWindowMonitor(loginWindow)
    // 加载窗口内容
    this.loadLoginWindowContent(loginWindow)
  }

  public getLoginWindow(): BrowserWindow | null {
    return this.loginWindow
  }

  // 基于 electron-vite cli 的渲染器的热模块替换。用于开发时加载远程 URL，或在生产时加载本地 HTML 文件
  private loadLoginWindowContent(loginWindow: BrowserWindow) {
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      loginWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/login')
    } else {
      let loadURL = electronServe({
        scheme: 'login',
        directory: join(__dirname, '../renderer')
      })
      loadURL(loginWindow, undefined, '/login')
    }
  }

  private setupWindowLifecycleEvents(loginWindow: BrowserWindow) {
    loginWindow.on('close', () => {
      if (app.isQuitting) {
        mainLogger.info('登录窗口由托盘关闭')
        return app.quit()
      }
      // 切换登录时 应完全退出窗口 不走hide
      if (app.isToggle) {
        mainLogger.info('登录窗口→主窗口')
        return
      }
      mainLogger.info('登录窗口关闭')
    })

    loginWindow.on('closed', () => {
      this.loginWindow = null
    })
  }

  private setupLoginWindowMonitor(loginWindow: BrowserWindow) {
    loginWindow.webContents.on('render-process-gone', (_, details) => {
      mainLogger.error(`【登录窗口】渲染进程崩溃: ${JSON.stringify(details)}`)
      const currentTime = Date.now()
      const lastCrashTime = this.lastRendererProcessCrashTime
      this.lastRendererProcessCrashTime = currentTime
      if (currentTime - lastCrashTime > 60 * 1000) {
        // 如果大于1分钟，则重启渲染进程
        loginWindow.webContents.reload()
      } else {
        // 如果小于1分钟，则退出应用, 可能是连续crash，需要退出应用
        app.exit(1)
      }
    })

    loginWindow.webContents.on('unresponsive', () => {
      // 在升级到electron 34后，可以获取具体js stack trace,目前只打个日志监控下
      // https://www.electronjs.org/blog/electron-34-0#unresponsive-renderer-javascript-call-stacks
      mainLogger.error('【登录窗口】渲染进程无响应')
    })
  }

  private setupWindowEvents(loginWindow: BrowserWindow) {
    loginWindow.once('ready-to-show', () => {
      mainLogger.info('登录窗口准备完成')
      loginWindow.show()
    })
  }

  public showLoginWindow() {
    mainLogger.info('登录窗口显示')
    if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      if (this.loginWindow.isMinimized()) {
        this.loginWindow.restore()
        return
      }

      /**
       * 关于 setVisibleOnAllWorkspaces
       *
       * [macOS] Known Issue
       *  setVisibleOnAllWorkspaces true/false 不会将窗口带到当前桌面（在 Windows 上正常工作）AppleScript 可能是一个解决方案，但不值得
       *
       * [Linux] Known Issue
       *  setVisibleOnAllWorkspaces 在 Linux 环境下（特别是 KDE Wayland）会导致窗口进入"假弹出"状态
       *  因此在 Linux 环境下不执行这两行代码
       */
      if (!isLinux) {
        this.loginWindow.setVisibleOnAllWorkspaces(true)
      }

      this.loginWindow.show()
      this.loginWindow.focus()
      if (!isLinux) {
        this.loginWindow.setVisibleOnAllWorkspaces(false)
      }
    } else {
      this.loginWindow = this.createLoginWindow()
    }
  }

  public toggleLoginWindow() {
    if (this.loginWindow && !this.loginWindow.isDestroyed() && this.loginWindow.isVisible()) {
      if (this.loginWindow.isFocused()) {
        this.loginWindow.hide()
        app.dock?.hide()
      } else {
        this.loginWindow.focus()
      }
      return
    }

    this.showLoginWindow()
  }

  public closeLoginWindow() {
    if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      this.loginWindow.close()
    }
  }
}

export const loginService = LoginService.getInstance()
