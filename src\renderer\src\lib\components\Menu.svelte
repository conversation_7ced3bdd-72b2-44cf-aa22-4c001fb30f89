<script lang="ts">
  import { tick } from 'svelte'
  import Icon from '@iconify/svelte'
  import { page } from '$app/state'
  import { type Attachment } from 'svelte/attachments'
  import IconMenuToClose from './icons/IconMenuToClose.svelte'
  import IconCloseToMenu from './icons/IconCloseToMenu.svelte'

  let { collapsed = $bindable(false), menuList = [] } = $props()

  function restartAnimation(event: MouseEvent, path: string) {
    if (page.route.id === path) return
    // 第一种: 重置所有动画
    const svg = event.currentTarget.querySelector('svg')
    if (!svg) return
    const animations = svg.querySelectorAll('animate, set')
    animations.forEach((anim) => {
      anim.beginElement()
    })

    // 第二种: 克隆并替换SVG
    // const iconContainer = event.currentTarget.querySelector('.iconify')
    // if (!iconContainer) return
    // const newIcon = iconContainer.cloneNode(true)
    // iconContainer.parentNode.replaceChild(newIcon, iconContainer)
  }

  function setDataTip(name: string): Attachment {
    return (node) => {
      const textElement = node.querySelector('.menu-text')
      if (collapsed) {
        node.setAttribute('data-tip', name || textElement.textContent)
      } else {
        setTimeout(() => {
          if (textElement.scrollWidth > textElement.clientWidth) {
            node.setAttribute('data-tip', name || textElement.textContent)
          } else {
            node.removeAttribute('data-tip')
          }
        }, 500)
      }
    }
  }
</script>

<aside
  class="sidebar group bg-base-200 relative flex h-full flex-col justify-between"
  class:collapsed
>
  <!-- 填充满 p-0 [&_li>*]:rounded-none -->
  <ul class="menu rounded-box mb-1 w-full flex-1 flex-shrink-0">
    {#each menuList as menu (menu.path)}
      <li class={menu.bottom ? 'mt-auto' : ''}>
        <a
          href={menu.path}
          class:menu-active={!menu.bottom && page.route.id === menu.path}
          class="tooltip tooltip-right relative gap-0"
          style="--menu-active-bg: oklch(55% 0.046 257.417);--menu-active-fg: var(--color-neutral-content)"
          role="button"
          tabindex="0"
          onclick={(event) => {
            if (!menu.bottom) {
              restartAnimation(event, menu.path)
            }
          }}
          {@attach setDataTip(menu.name)}
        >
          <Icon class="menu-icon" icon={menu.icon} />
          <span class="menu-text">{menu.name}</span>
          <!-- <div class="status status-error absolute top-3.5 left-0 animate-bounce"></div> -->
        </a>
      </li>
    {/each}
  </ul>
  <!-- <button
    class="mb-5 ml-5 w-fit cursor-pointer hover:text-gray-500"
    onclick={() => (collapsed = !collapsed)}
  >
    {#if collapsed}
      <IconCloseToMenu />
    {:else}
      <IconMenuToClose />
    {/if}
  </button> -->
  <button
    class="text-base-content/50 hover:text-base-content tooltip tooltip-right bg-base-200 absolute top-[calc(50%-(var(--spacing)*12/2))] -right-2.5 h-12 cursor-pointer rounded-md opacity-0 transition-[background-color,opacity,color] group-hover:opacity-100 hover:bg-gray-200 dark:hover:bg-black"
    data-tip={collapsed ? '展开' : '收起'}
    onclick={() => (collapsed = !collapsed)}
  >
    <Icon
      class="transition-transform {collapsed && 'rotate-180'}"
      icon="line-md:chevron-small-double-left"
      width="20"
      height="20"
    />
  </button>
</aside>

<style>
  .sidebar {
    --sidebar-width: 150px;
    --icon-size: 18px;
    --text-opacity: 1;

    width: var(--sidebar-width);
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  :global(.menu-icon) {
    width: var(--icon-size);
    height: var(--icon-size);
    transition: transform 0.3s ease;
  }

  .sidebar.collapsed {
    --sidebar-width: 60px;
    --text-opacity: 0;

    .menu-text {
      margin-left: 0;
    }
  }

  .menu-text {
    opacity: var(--text-opacity);
    transition:
      opacity 0.3s ease,
      margin-left 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 6px;
  }
</style>
