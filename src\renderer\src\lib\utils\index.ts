import rendererLogger from './logger'

/**
 * 判断是否为空
 * @param {any} param
 */
export function isEmpty(param: any) {
  return [undefined, null, ''].includes(param)
}

/**
 * zod错误信息转换
 * @param {string} errorInfo zod错误信息
 * @returns {Record<string, string>} 错误信息
 */
export const zodErrorToRecord = (errorInfo: string) => {
  try {
    const errors = JSON.parse(errorInfo)
    const errorsInfo: Record<string, string> = {}
    errors.forEach((err: any) => {
      const fieldName = err.path[0]
      errorsInfo[fieldName] = err.message
    })
    return errorsInfo
  } catch {
    rendererLogger.error('转换失败, zod错误信息格式错误: ', errorInfo)
    return {}
  }
}

/**
 * 生成枚举数据组
 * @param rest 参数, 参数数量必须为偶数, 奇数为id, 偶数为label
 * @returns 枚举数据组
 */
export function generatorDataGroup(...rest: any[]) {
  if (!rest) {
    return []
  }

  if (rest.length % 2 !== 0) {
    throw new Error('参数数量不匹配')
  }
  const list = []
  const map = {}
  for (let i = 0; i < rest.length; i += 2) {
    const id = rest[i]
    const label = rest[i + 1]
    list.push({ id, label })
    Object.assign(map, { [id]: label })
  }
  return [list, map]
}

/**
 * 文本截取(取前3个和后3个, 中间用***表示)
 * @param {string} text 文本
 * @returns {string} 截取后的文本
 */
export function truncateText(text: string): string {
  if (!text || text.length <= 6) {
    return text
  }
  return text.slice(0, 3) + '***' + text.slice(-3)
}
