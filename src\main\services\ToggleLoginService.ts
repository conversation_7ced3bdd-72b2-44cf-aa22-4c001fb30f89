import { windowService } from './WindowService'
import { loginService } from './LoginService'
import { app, ipcMain } from 'electron'
import { IpcChannel } from '@shared/IpcChannel'
import { getLoginState, setToken } from '@main/ipc/handle'

class ToggleLoginService {
  private isIpcHandlerRegistered = false
  private static instance: ToggleLoginService | null = null

  public static getInstance(): ToggleLoginService {
    if (!this.instance) {
      this.instance = new ToggleLoginService()
    }
    return this.instance
  }

  public registerIpcHandler() {
    if (this.isIpcHandlerRegistered) return
    /** 重启 */
    ipcMain.handle(IpcChannel.App_Reload, () => {
      const isLogin = getLoginState()
      if (isLogin) {
        const mainWindow = windowService.getMainWindow()
        if (mainWindow) {
          mainWindow.reload()
        }
      } else {
        const loginWindow = loginService.getLoginWindow()
        if (loginWindow) {
          loginWindow.reload()
        }
      }
    })
    /** 设置登录状态 */
    ipcMain.handle(IpcChannel.App_ToggleLoginState, (_, token?: string) => {
      setToken(token)
      app.isToggle = true
      if (token) {
        windowService.showMainWindow()
        loginService.closeLoginWindow()
      } else {
        loginService.showLoginWindow()
        windowService.closeMainWindow()
      }
      app.isToggle = false
    })
    this.isIpcHandlerRegistered = true
  }
}

export const toggleLoginService = ToggleLoginService.getInstance()
