import { IpcChannel } from '@shared/IpcChannel'
import { ThemeMode } from '@types'

let theme = $state<ThemeMode>(
  window.matchMedia('(prefers-color-scheme: dark)').matches ? ThemeMode.dark : ThemeMode.light
)

export const getTheme = () => theme

const setTheme = (newTheme: ThemeMode) => {
  theme = newTheme
}

window.electron.ipcRenderer.on(IpcChannel.ThemeUpdated, (_, newTheme: ThemeMode) => {
  setTheme(newTheme)
})
