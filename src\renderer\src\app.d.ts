/// <reference types="vite/client" />
/// <reference types="@sveltejs/kit" />
/// <reference types="svelte" />

// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
  namespace App {
    // interface Error {}
    // interface Locals {}
    // interface PageData {}
    // interface PageState {}
    // interface Platform {}
  }
}

import { ElectronAPI } from '@electron-toolkit/preload'
import type { WindowApiType } from '../../preload'

declare global {
  interface Window {
    electron: ElectronAPI
    api: WindowApiType
  }
}

export {}
