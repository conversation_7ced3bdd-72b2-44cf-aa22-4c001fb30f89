interface SuccessResponse<D> {
  /** 响应状态 */
  status: true
  /** 消息提示 */
  message: string
  /** 响应数据 */
  data: D
  /** 错误信息 */
  errors?: never
}

interface ErrorResponse<E> {
  /** 响应状态 */
  status: false
  /** 消息提示 */
  message: string
  /** 响应数据 */
  data?: never
  /** 错误信息 */
  errors: E
}
/** 响应体-base */
type ResponseBase<D = object, E = []> = SuccessResponse<D> | ErrorResponse<E>

export type LoginResponseData = ResponseBase<{ token: string }>

export interface LoginFormData {
  /** 用户名 */
  login: string
  /** 密码 */
  password: string
}

export interface UserData {
  /** 用户id */
  id: number
  /** 邮箱 */
  email: string
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 头像 */
  avatar: string
  /** 性别 1: 男 0: 女 */
  gender: number
  /** 用户角色 1: 普通用户 100: 管理员 */
  role: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

export type UserInfoResponseData = ResponseBase<UserData>
