import dayjs from 'dayjs'
import { SQLiteConnection } from '../connection'
import { sqlLogger } from '@main/utils/logger'
import type { AugmentAccountRow, AugmentAccountInsert } from '@types'

export class AugmentAccount {
  private connection: SQLiteConnection
  private readonly validKeys = ['id', 'email', 'token']

  constructor(connection: SQLiteConnection) {
    this.connection = connection
    this.init()
  }

  private init() {
    this.connection.db.exec(`
      CREATE TABLE IF NOT EXISTS AugmentAccount (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT, -- 邮箱
        token TEXT NOT NULL, -- 令牌
        usage_balance_depleted INTEGER NOT NULL DEFAULT 1, -- 额度是否用完, 0 for false, 1 for true
        end_date TEXT, -- 结束日期
        desc TEXT, -- 描述
        created_at TEXT NOT NULL, -- 创建日期
        tenant_url TEXT NOT NULL DEFAULT 'https://d9.api.augmentcode.com/', -- 租户URL
        scopes TEXT NOT NULL DEFAULT '["email"]' -- 范围
      )
    `)
    this.connection.db.exec(
      'CREATE INDEX IF NOT EXISTS idx_augment_account_email ON AugmentAccount(email)'
    )
  }

  public get(value: string | number, key: string = 'email'): AugmentAccountRow | null {
    if (!this.validKeys.includes(key)) {
      sqlLogger.error('get error: ', `Invalid key: ${key}`)
      throw new Error(`Invalid key: ${key}`)
    }
    const stmt = this.connection.db.prepare(`SELECT * FROM AugmentAccount WHERE ${key} = ?`)
    const row = stmt.get(value) as AugmentAccountRow
    return row
  }

  public getAll(): AugmentAccountRow[] {
    const stmt = this.connection.db.prepare('SELECT * FROM AugmentAccount')
    const rows = stmt.all() as AugmentAccountRow[]
    return rows
  }

  public set(row: AugmentAccountInsert) {
    const stmt = this.connection.db.prepare(
      'INSERT INTO AugmentAccount (email, token, usage_balance_depleted, end_date, desc, created_at, tenant_url, scopes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
    )
    stmt.run(
      row.email,
      row.token,
      row.usage_balance_depleted ?? 0,
      row.end_date,
      row.desc,
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
      row.tenant_url || 'https://d9.api.augmentcode.com/',
      row.scopes || '["email"]'
    )
  }

  public update(row: Partial<AugmentAccountRow> & { id: number }) {
    const fields: string[] = []
    const values: (string | number | null)[] = []
    Object.entries(row).forEach(([key, value]) => {
      if (key !== 'id' && value !== undefined) {
        fields.push(`${key} = ?`)
        values.push(value)
      }
    })
    if (fields.length === 0) return
    const stmt = this.connection.db.prepare(
      `UPDATE AugmentAccount SET ${fields.join(', ')} WHERE id = ?`
    )
    stmt.run(...values, row.id)
  }

  public batchSet(rows: Partial<AugmentAccountRow>[]) {
    this.connection.transaction(() => {
      const stmt = this.connection.db.prepare(`
        INSERT INTO AugmentAccount (email, token, usage_balance_depleted, end_date, desc, created_at, tenant_url, scopes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `)
      rows.forEach((row) => {
        stmt.run(
          row.email,
          row.token,
          row.usage_balance_depleted ?? 0,
          row.end_date,
          row.desc,
          dayjs().format('YYYY-MM-DD HH:mm:ss'),
          row.tenant_url ?? 'https://d9.api.augmentcode.com/',
          row.scopes ?? '["email"]'
        )
      })
    })
  }

  public delete(value: string | number, key: string = 'id'): boolean {
    if (!this.validKeys.includes(key)) {
      sqlLogger.error('delete error: ', `Invalid key: ${key}`)
      throw new Error(`Invalid key: ${key}`)
    }
    const stmt = this.connection.db.prepare(`DELETE FROM AugmentAccount WHERE ${key} = ?`)
    const result = stmt.run(value)
    return result.changes > 0
  }

  public clear() {
    this.connection.transaction(() => {
      this.connection.db.prepare('DELETE FROM AugmentAccount').run()
      this.connection.db.prepare('DELETE FROM sqlite_sequence WHERE name = ?').run('AugmentAccount')
      // 或者使用 UPDATE 替代 DELETE（效果类似）
      // this.connection.db.prepare(
      //   'UPDATE sqlite_sequence SET seq = 0 WHERE name = ?'
      // ).run('AugmentAccount');
    })
  }
}
