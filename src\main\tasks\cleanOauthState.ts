import schedule from 'node-schedule'
import dayjs from 'dayjs'
import { database } from '@main/db'
import { mainLogger } from '@main/utils/logger'

export function cleanOauthState() {
  // 每2分钟执行一次
  schedule.scheduleJob('*/2 * * * *', async () => {
    const oauthState = await database.items.get('oauthState')
    if (!oauthState) return
    // 超时, 删除
    if (dayjs(oauthState.creationTime).isBefore(dayjs())) {
      mainLogger.info('oauthState超时 delete, creationTime:', oauthState.creationTime)
      database.items.delete('oauthState')
    }
  })
}
