<script lang="ts">
  import { onMount } from 'svelte'
  import nprogress from 'nprogress'
  import Icon from '@iconify/svelte'
  import type { WebviewTag } from 'electron/renderer'

  let webview: WebviewTag | null = $state(null)

  onMount(() => {
    const loadStart = () => {
      nprogress.start()
    }

    const loadStop = () => {
      nprogress.done()
    }

    const executeScript = () => {
      webview.executeJavaScript(`
        // 重写 window.open，强制在当前页面跳转
        const originalWindowOpen = window.open
        window.open = function(url, target, features) {
          if (url === 'https://rapidapi.com/pranavkdileep/api/tempmail-api-free') {
            // 直接修改当前页面的 location
            window.location.href = url
            return null // 返回 null 避免新窗口
          }
          return originalWindowOpen.apply(this, arguments)
        }
        // 替换所有 target="_blank" 的 <a> 标签
        document.querySelectorAll('a[target="_blank"]').forEach(a => {
          a.removeAttribute('target')
        })
      `)
    }

    webview.addEventListener('dom-ready', executeScript)
    // 页面加载开始与结束
    webview.addEventListener('did-start-loading', loadStart)
    webview.addEventListener('did-stop-loading', loadStop)
    // 导航开始与结束
    // webview.addEventListener('did-navigate-in-page', loadStart)
    // webview.addEventListener('did-finish-load', loadstop)
    const willNavigate = (event: Event) => {
      console.log('will-navigate')
    }
    webview.addEventListener('will-navigate', willNavigate)
    return () => {
      webview.removeEventListener('dom-ready', executeScript)
      webview.removeEventListener('did-start-loading', loadStart)
      webview.removeEventListener('did-stop-loading', loadStop)
      webview.removeEventListener('will-navigate', willNavigate)
      loadStop()
    }
  })

  const goBack = () => {
    if (webview && webview.canGoBack()) {
      webview.goBack()
    }
  }

  const goForward = () => {
    if (webview && webview.canGoForward()) {
      webview.goForward()
    }
  }

  const reload = () => {
    if (webview) {
      webview.reload()
    }
  }

  const openInBrowser = () => {
    if (webview) {
      const url = webview.getURL()
      window.api.openWebsite(url)
    }
  }
</script>

<div class=" relative flex h-full flex-col">
  <div
    class="hover:bg-base-200 absolute -top-2 left-1/2 flex w-fit -translate-x-1/2 items-center gap-2 rounded-xl bg-white px-3"
  >
    <button class="nav-button" data-tip="后退" onclick={goBack}>
      <Icon icon="mdi-light:arrow-left" width="24" height="24" />
    </button>
    <button class="nav-button" data-tip="前进" onclick={goForward}>
      <Icon icon="mdi-light:arrow-right" width="24" height="24" />
    </button>
    <button class="nav-button" data-tip="刷新" onclick={reload}>
      <Icon icon="material-symbols-light:refresh" width="24" height="24" />
    </button>
    <button class="nav-button" data-tip="在浏览器中打开" onclick={openInBrowser}>
      <Icon icon="fluent:open-20-regular" width="20" height="20" />
    </button>
  </div>
  <webview bind:this={webview} src="https://tempmail.mida.vip" style="flex: 1"></webview>
</div>

<style lang="postcss">
  @reference '@assets/styles/index.css';
  .nav-button {
    @apply tooltip tooltip-bottom text-base-content/50 hover:text-base-content cursor-pointer rounded-full transition-colors;
  }
</style>
