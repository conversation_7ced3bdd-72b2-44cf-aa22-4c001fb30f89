@import 'tailwindcss';
/* @plugin '@tailwindcss/forms'; */
@plugin '@tailwindcss/typography';
@plugin "daisyui";
@import './theme.css';
@import './color.css';

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root {
  --navbar-height: calc(var(--spacing) * 10);
}

* {
  -webkit-user-drag: none;
}

@utility nav-h {
  height: var(--navbar-height);
}

@utility validator-error {
  @apply mt-2 block text-xs text-[var(--color-error)];
}

.drag {
  app-region: drag;
}

.no-drag {
  app-region: no-drag;
}

/* 覆盖daisyUI的scrollbar-gutter设置 */
:root:has(
    .modal-open,
    .modal[open],
    .modal:target,
    .modal-toggle:checked,
    .drawer:not(.drawer-open) > .drawer-toggle:checked
  ) {
  scrollbar-gutter: auto;
}

:where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
  scrollbar-gutter: auto;
}

.modal {
  top: var(--navbar-height);
}

.modal-box {
  margin-top: calc(var(--navbar-height) * -3);
}

.toaster > .wrapper > .base > .indicator > .status {
  height: auto !important;
  width: auto !important;
}
