export enum IpcChannel {
  /** ipc-获取应用信息 */
  App_Info = 'app:info',
  /** ipc-设置语言 */
  App_SetLanguage = 'app:setLanguage',
  /** ipc-重启窗口 */
  App_Reload = 'app:reload',
  /** ipc-重启应用 */
  App_Restart = 'app:restart',
  /** ipc-通知 */
  Notification_Send = 'notification:send',
  /** ipc-配置修改 */
  Config_Set = 'config:set',
  /** ipc-配置获取 */
  Config_Get = 'config:get',
  /** ipc-获取主题 */
  App_GetTheme = 'app:getTheme',
  /** ipc-设置主题 */
  App_SetTheme = 'app:setTheme',
  /** ipc-主题更新 */
  ThemeUpdated = 'theme:updated',
  /** ipc-获取IDE */
  App_GetIDE = 'app:getIDE',
  /** ipc-设置IDE */
  App_SetIDE = 'app:setIDE',
  /** ipc-打开路径(文件夹) */
  App_OpenPath = 'app:openPath',
  /** ipc-打开文件 */
  App_OpenFile = 'app:openFile',
  /** ipc-下载文件 */
  App_DownloadFile = 'app:downloadFile',
  /** ipc-下载文件进度 */
  App_DownloadFileProgress = 'app:downloadFileProgress',
  /** ipc-清除缓存 */
  App_ClearCache = 'app:clearCache',
  /** ipc-获取缓存大小 */
  App_GetCacheSize = 'app:get-cache-size',
  /** ipc-清除文件 */
  App_ClearFiles = 'app:clearFiles',
  /** ipc-清除备份 */
  App_ClearBackups = 'app:clearBackups',
  /** ipc-清除日志 */
  App_ClearLogs = 'app:clearLogs',
  /** ipc-压缩 */
  Zip_Compress = 'zip:compress',
  /** ipc-解压 */
  Zip_Decompress = 'zip:decompress',
  /** ipc-快捷键更新 */
  Shortcuts_Update = 'shortcuts:update',
  /** ipc-aes加密 */
  Aes_Encrypt = 'aes:encrypt',
  /** ipc-aes解密 */
  Aes_Decrypt = 'aes:decrypt',
  /** ipc-Store 同步订阅 */
  StoreSync_Subscribe = 'store-sync:subscribe',
  /** ipc-Store 同步取消订阅 */
  StoreSync_Unsubscribe = 'store-sync:unsubscribe',
  /** ipc-Store 同步更新 */
  StoreSync_OnUpdate = 'store-sync:on-update',
  /** ipc-Store 同步广播 */
  StoreSync_BroadcastSync = 'store-sync:broadcast-sync',
  /** ipc-切换登录 */
  App_ToggleLoginState = 'app-auth:toggle-login-state',
  /** ipc-获取token */
  App_GetToken = 'app-auth:get-token',
  /** ipc-设置token */
  App_SetToken = 'app-auth:set-token',
  /** ipc-获取登录信息 */
  App_GetRememberLoginInfo = 'app-auth:get-remember-login-info',
  /** ipc-设置登录信息 */
  App_SetRememberLoginInfo = 'app-auth:set-remember-login-info',
  /** ipc-打开配置文件 */
  Config_OpenEditor = 'config:open-editor',
  /** ipc-以默认方式打开外部协议URL */
  Open_Website = 'open:website',
  /** ipc-ItemTable数据表操作-获取所有数据 */
  DB_ItemTable_GetAll = 'db:ItemTable-getAll',
  /** ipc-ItemTable数据表操作-获取指定key数据 */
  DB_ItemTable_Get = 'db:ItemTable-get',
  /** ipc-ItemTable数据表操作-设置数据 */
  DB_ItemTable_Set = 'db:ItemTable-set',
  /** ipc-ItemTable数据表操作-删除数据 */
  DB_ItemTable_Delete = 'db:ItemTable-delete',
  /** ipc-ItemTable数据表操作-批量设置数据 */
  DB_ItemTable_BatchSet = 'db:ItemTable-batchSet',
  /** ipc-AugmentAccount数据表操作-获取所有数据 */
  DB_AugmentAccount_GetAll = 'db:AugmentAccount-getAll',
  /** ipc-AugmentAccount数据表操作-获取指定key数据 */
  DB_AugmentAccount_Get = 'db:AugmentAccount-get',
  /** ipc-AugmentAccount数据表操作-设置数据 */
  DB_AugmentAccount_Set = 'db:AugmentAccount-set',
  /** ipc-AugmentAccount数据表操作-更新数据 */
  DB_AugmentAccount_Update = 'db:AugmentAccount-update',
  /** ipc-AugmentAccount数据表操作-删除数据 */
  DB_AugmentAccount_Delete = 'db:AugmentAccount-delete',
  /** ipc-AugmentAccount数据表操作-批量设置数据 */
  DB_AugmentAccount_BatchSet = 'db:AugmentAccount-batchSet',
  /** ipc-AugmentAccount数据表操作-清空数据 */
  DB_AugmentAccount_Clear = 'db:AugmentAccount-clear',
  /** ipc-VSCode数据表操作-获取所有数据 */
  DB_VSCode_GetAll = 'db:VSCode-getAll',
  /** ipc-VSCode数据表操作-获取指定key数据 */
  DB_VSCode_Get = 'db:VSCode-get',
  /** ipc-VSCode数据表操作-设置数据 */
  DB_VSCode_Set = 'db:VSCode-set',
  /** ipc-VSCode数据表操作-删除数据 */
  DB_VSCode_Delete = 'db:VSCode-delete',
  /** ipc-VSCode数据表操作-批量设置数据 */
  DB_VSCode_BatchSet = 'db:VSCode-batchSet',
  /** ipc-secrets-加密 */
  Secrets_Encrypt = 'secrets:encrypt',
  /** ipc-secrets-解密 */
  Secrets_Decrypt = 'secrets:decrypt',
  /** ipc-生成登录链接 */
  App_GenerateLoginUrl = 'app-auth:generate-login-url',
  /** ipc-校验code */
  App_VerifyCode = 'app-auth:verify-code',
  /** ipc-渲染进程日志 */
  Renderer_Log = 'renderer:log',
  /** ipc-获取augment扩展信息 */
  App_GetAugmentInfo = 'app:get-augment-info',
  /** ipc-获取软件信息 */
  App_GetSoftwareInfo = 'app:get-software-info',
  /** ipc-获取chrome版本 */
  App_GetChromeVersion = 'app:get-chrome-version',
  /** ipc-写入剪切板文本 */
  App_WriteText = 'app:copy-text',
  /** ipc-读取剪切板文本 */
  App_ReadText = 'app:read-text'
}
