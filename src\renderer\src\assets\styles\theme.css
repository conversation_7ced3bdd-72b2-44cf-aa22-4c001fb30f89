@plugin "daisyui" {
  themes:
    light --default,
    dark --prefersdark,
    bumblebee;
}

@plugin "daisyui/theme" {
  name: 'pastet';
  default: true;
  prefersdark: false;
  color-scheme: 'light';
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(20% 0 0);
  --color-primary: oklch(90% 0.063 306.703);
  --color-primary-content: oklch(66% 0.295 322.15);
  --color-secondary: oklch(89% 0.058 10.001);
  --color-secondary-content: oklch(51% 0.222 16.935);
  --color-accent: oklch(90% 0.093 164.15);
  --color-accent-content: oklch(50% 0.118 165.612);
  --color-neutral: oklch(14% 0.005 285.823);
  --color-neutral-content: oklch(92% 0.004 286.32);
  --color-info: oklch(86% 0.127 207.078);
  --color-info-content: oklch(52% 0.105 223.128);
  --color-success: oklch(87% 0.15 154.449);
  --color-success-content: oklch(52% 0.154 150.069);
  --color-warning: oklch(83% 0.128 66.29);
  --color-warning-content: oklch(55% 0.195 38.402);
  --color-error: oklch(80% 0.114 19.571);
  --color-error-content: oklch(50% 0.213 27.518);
  --radius-selector: 1rem;
  --radius-field: 0.5rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@plugin "daisyui/theme" {
  name: 'dark';
  default: false;
  prefersdark: false;
  color-scheme: 'dark';

  --radius-selector: 1rem;
  --radius-field: 0.5rem;
}

@plugin "daisyui/theme" {
  name: 'light';
  default: false;
  prefersdark: false;
  color-scheme: 'light';
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(45% 0.24 277.023);
  --color-primary-content: oklch(93% 0.034 272.788);
  --color-secondary: oklch(65% 0.241 354.308);
  --color-secondary-content: oklch(94% 0.028 342.258);
  --color-accent: oklch(77% 0.152 181.912);
  --color-accent-content: oklch(38% 0.063 188.416);
  --color-neutral: oklch(14% 0.005 285.823);
  --color-neutral-content: oklch(92% 0.004 286.32);
  --color-info: oklch(74% 0.16 232.661);
  --color-info-content: oklch(29% 0.066 243.157);
  --color-success: oklch(76% 0.177 163.223);
  --color-success-content: oklch(37% 0.077 168.94);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(41% 0.112 45.904);
  --color-error: oklch(71% 0.194 13.428);
  --color-error-content: oklch(27% 0.105 12.094);
  --radius-selector: 1rem;
  --radius-field: 0.3rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}
