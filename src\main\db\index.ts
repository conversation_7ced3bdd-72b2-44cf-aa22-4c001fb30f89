import path from 'node:path'
import { SQLiteConnection } from './connection'
import { ItemTable } from './tables/ItemTable'
import { getDataPath, getIDEDatabasePath } from '@main/utils/paths'
import { isDev } from '@main/constant'
import { AugmentAccount } from './tables/AugmentAccount'

class DatabaseOperation {
  protected readonly connection: SQLiteConnection
  constructor(connection: SQLiteConnection) {
    this.connection = connection
  }

  public async close(): Promise<void> {
    await this.connection.close()
  }

  public async checkIntegrity(full: boolean = false): Promise<string> {
    return this.connection.checkIntegrity(full)
  }

  public async backup(): Promise<void> {
    return this.connection.backup()
  }
}

class MyDatabase extends DatabaseOperation {
  public readonly items: ItemTable
  public readonly augmentAccount: AugmentAccount

  constructor(dbPath: string, options: { logging?: boolean } = {}) {
    const connection = new SQLiteConnection(dbPath, {
      busyTimeout: 5000,
      logging: options.logging
    })
    super(connection)
    // 初始化所有表
    this.items = new ItemTable(this.connection, true)
    this.augmentAccount = new AugmentAccount(this.connection)
  }
}

class VScodeDatabase extends DatabaseOperation {
  public readonly items: ItemTable

  constructor(dbPath: string, options: { logging?: boolean; initDatabase?: boolean } = {}) {
    const connection = new SQLiteConnection(dbPath, {
      busyTimeout: 5000,
      logging: options.logging
    })
    super(connection)
    this.items = new ItemTable(this.connection, false)
  }
}

const dbPath = path.join(getDataPath(), 'data.db')
export const database = new MyDatabase(dbPath, { logging: isDev })
export const vscodeDatabase = new VScodeDatabase(getIDEDatabasePath(), { logging: isDev })
