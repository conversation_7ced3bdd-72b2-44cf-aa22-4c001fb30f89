import { contextBridge, ipc<PERSON>enderer } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { IpcChannel } from '@shared/IpcChannel'
import {
  IDE,
  type RememberLoginInfo,
  type Shortcut,
  ThemeMode,
  type AugmentAccountRow,
  type AugmentAccountInsert
} from '@types'

// renderer自定义API
const api = {
  /** 获取应用信息 */
  getAppInfo: () => ipcRenderer.invoke(IpcChannel.App_Info),
  /** 渲染进程日志 */
  rendererLog: (level: 'info' | 'debug' | 'warn' | 'error', ...args: any[]) =>
    ipcRenderer.invoke(IpcChannel.Renderer_Log, level, ...args),
  /** 重启窗口 */
  reload: () => ipcRenderer.invoke(IpcChannel.App_Reload),
  /** 重启应用 */
  restart: () => ipcRenderer.invoke(IpcChannel.App_Restart),
  /** 通知 */
  notification: {
    send: (notification: Notification) =>
      ipcRenderer.invoke(IpcChannel.Notification_Send, notification)
  },
  /** 设置语言 */
  setLanguage: (lang: string) => ipcRenderer.invoke(IpcChannel.App_SetLanguage, lang),
  /** 配置 */
  config: {
    /** 配置修改 */
    set: (key: string, value: any, isNotify: boolean = false) =>
      ipcRenderer.invoke(IpcChannel.Config_Set, key, value, isNotify),
    /** 配置获取 */
    get: (key: string) => ipcRenderer.invoke(IpcChannel.Config_Get, key)
  },
  /** 获取主题 */
  getTheme: () => ipcRenderer.invoke(IpcChannel.App_GetTheme),
  /** 设置主题 */
  setTheme: (theme: ThemeMode) => ipcRenderer.invoke(IpcChannel.App_SetTheme, theme),
  /** 获取IDE */
  getIDE: () => ipcRenderer.invoke(IpcChannel.App_GetIDE),
  /** 设置IDE */
  setIDE: (ide: IDE) => ipcRenderer.invoke(IpcChannel.App_SetIDE, ide),
  /** 打开路径 */
  openPath: (path: string) => ipcRenderer.invoke(IpcChannel.App_OpenPath, path),
  /** 下载文件 */
  downloadFile: (url: string, fileName?: string, openDialog?: boolean) =>
    ipcRenderer.invoke(IpcChannel.App_DownloadFile, url, fileName, openDialog),
  /** 获取缓存大小 */
  getCacheSize: () => ipcRenderer.invoke(IpcChannel.App_GetCacheSize),
  /** 清除缓存 */
  clearCache: () => ipcRenderer.invoke(IpcChannel.App_ClearCache),
  /** 清除文件 */
  clearFiles: () => ipcRenderer.invoke(IpcChannel.App_ClearFiles),
  /** 清除备份 */
  clearBackups: () => ipcRenderer.invoke(IpcChannel.App_ClearBackups),
  /** 清除日志 */
  clearLogs: () => ipcRenderer.invoke(IpcChannel.App_ClearLogs),
  /** 解压缩 */
  zip: {
    /** 压缩 */
    compress: (text: string) => ipcRenderer.invoke(IpcChannel.Zip_Compress, text),
    /** 解压 */
    decompress: (text: Buffer) => ipcRenderer.invoke(IpcChannel.Zip_Decompress, text)
  },
  /** 快捷键 */
  shortcuts: {
    /** 快捷键更新 */
    update: (shortcuts: Shortcut[]) => ipcRenderer.invoke(IpcChannel.Shortcuts_Update, shortcuts)
  },
  /** aes加解密 */
  aes: {
    /** 加密 */
    encrypt: (text: string) => ipcRenderer.invoke(IpcChannel.Aes_Encrypt, text),
    /** 解密 */
    decrypt: (encryptedData: string) => ipcRenderer.invoke(IpcChannel.Aes_Decrypt, encryptedData)
  },

  /** store同步 */
  storeSync: {
    /** 订阅 */
    subscribe: () => ipcRenderer.invoke(IpcChannel.StoreSync_Subscribe),
    /** 取消订阅 */
    unsubscribe: () => ipcRenderer.invoke(IpcChannel.StoreSync_Unsubscribe),
    /** 更新 */
    onUpdate: (action: any) => ipcRenderer.invoke(IpcChannel.StoreSync_OnUpdate, action)
  },
  /** 获取登录 */
  login: {
    /** 获取token */
    getToken: () => ipcRenderer.invoke(IpcChannel.App_GetToken),
    /** 设置token */
    setToken: (token?: string) => ipcRenderer.invoke(IpcChannel.App_SetToken, token),
    /** 切换登录状态 */
    toggleLoginState: (token?: string) =>
      ipcRenderer.invoke(IpcChannel.App_ToggleLoginState, token),
    /** 获取登录信息 */
    getRememberLoginInfo: () => ipcRenderer.invoke(IpcChannel.App_GetRememberLoginInfo),
    /** 设置登录信息 */
    setRememberLoginInfo: (loginInfo: RememberLoginInfo) =>
      ipcRenderer.invoke(IpcChannel.App_SetRememberLoginInfo, loginInfo)
  },
  /** 打开配置文件 */
  openConfigEditor: () => ipcRenderer.invoke(IpcChannel.Config_OpenEditor),

  /** 以默认方式打开外部协议URL */
  openWebsite: (url: string) => ipcRenderer.invoke(IpcChannel.Open_Website, url),

  db: {
    /** ItemTable表 */
    items: {
      /** ItemTable-获取所有数据 */
      getAll: () => ipcRenderer.invoke(IpcChannel.DB_ItemTable_GetAll),
      /** ItemTable-获取指定key数据 */
      get: (key: string) => ipcRenderer.invoke(IpcChannel.DB_ItemTable_Get, key),
      /** ItemTable-设置数据 */
      set: (key: string, item: any) => ipcRenderer.invoke(IpcChannel.DB_ItemTable_Set, key, item),
      /** ItemTable-删除数据 */
      delete: (key: string) => ipcRenderer.invoke(IpcChannel.DB_ItemTable_Delete, key),
      /** ItemTable-批量设置数据 */
      batchSet: (items: Map<string, any>) =>
        ipcRenderer.invoke(IpcChannel.DB_ItemTable_BatchSet, items)
    },
    /** AugmentAccount表 */
    accounts: {
      /** AugmentAccount-获取所有数据 */
      getAll: () =>
        ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_GetAll) as Promise<AugmentAccountRow[]>,
      /** AugmentAccount-获取指定key数据 */
      get: (value: string | number, key?: string) =>
        ipcRenderer.invoke(
          IpcChannel.DB_AugmentAccount_Get,
          value,
          key
        ) as Promise<AugmentAccountRow | null>,
      /** AugmentAccount-设置数据 */
      set: (row: AugmentAccountInsert) =>
        ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_Set, row) as Promise<void>,
      /** AugmentAccount-更新数据 */
      update: (row: Partial<AugmentAccountRow> & { id: number }) =>
        ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_Update, row) as Promise<void>,
      /** AugmentAccount-删除数据 */
      delete: (value: string | number, key?: string) =>
        ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_Delete, value, key) as Promise<boolean>,
      /** AugmentAccount-批量设置数据 */
      batchSet: (rows: Partial<AugmentAccountRow>[]) =>
        ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_BatchSet, rows) as Promise<void>,
      /** AugmentAccount-清空数据 */
      clear: () => ipcRenderer.invoke(IpcChannel.DB_AugmentAccount_Clear) as Promise<void>
    },
    /** VSCode表 */
    vscode: {
      /** VSCode-获取所有数据 */
      getAll: () => ipcRenderer.invoke(IpcChannel.DB_VSCode_GetAll),
      /** VSCode-获取指定key数据 */
      get: (key: string) => ipcRenderer.invoke(IpcChannel.DB_VSCode_Get, key),
      /** VSCode-设置数据 */
      set: (key: string, item: any) => ipcRenderer.invoke(IpcChannel.DB_VSCode_Set, key, item),
      /** VSCode-删除数据 */
      delete: (key: string) => ipcRenderer.invoke(IpcChannel.DB_VSCode_Delete, key),
      /** VSCode-批量设置数据 */
      batchSet: (items: Map<string, any>) =>
        ipcRenderer.invoke(IpcChannel.DB_VSCode_BatchSet, items)
    }
  },

  /** secrets */
  secrets: {
    /** 加密 */
    encrypt: (data: any) => ipcRenderer.invoke(IpcChannel.Secrets_Encrypt, data),
    /** 解密 */
    decrypt: (data: number[]) => ipcRenderer.invoke(IpcChannel.Secrets_Decrypt, data)
  },

  /** 生成登录链接 */
  generateLoginUrl: () => ipcRenderer.invoke(IpcChannel.App_GenerateLoginUrl),
  /** 校验code */
  verifyCode: (code: string, tenantUrl: string, redirectUri: string) =>
    ipcRenderer.invoke(IpcChannel.App_VerifyCode, code, tenantUrl, redirectUri),
  /** 获取扩展信息 */
  getExtensionsInfo: () => ipcRenderer.invoke(IpcChannel.App_GetAugmentInfo),
  /** 获取软件信息 */
  getSoftwareInfo: () => ipcRenderer.invoke(IpcChannel.App_GetSoftwareInfo),
  /** 获取chrome版本 */
  getChromeVersion: () => ipcRenderer.invoke(IpcChannel.App_GetChromeVersion),
  clipboard: {
    /** 写入剪切板文本 */
    writeText: (text: string) => ipcRenderer.invoke(IpcChannel.App_WriteText, text),
    /** 读取剪切板文本 */
    readText: () => ipcRenderer.invoke(IpcChannel.App_ReadText)
  }
}

// 仅在启用上下文隔离时使用 `contextBridge` API 将 Electron API 暴露给渲染器，否则只需添加到 DOM 全局中。
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}

export type WindowApiType = typeof api
