import { Notification as ElectronNotification } from 'electron'
import { Notification } from '@shared/notification'
import icon from '../../../build/icon.png?asset'
import { windowService } from './WindowService'
import { loginService } from './LoginService'
import { getLoginState } from '@main/ipc/handle'

export function sendNotification(notification: Notification) {
  // 使用 Electron Notification API
  const electronNotification = new ElectronNotification({
    title: notification.title,
    body: notification.message,
    icon: icon
  })

  electronNotification.on('click', () => {
    const isLogin = getLoginState()
    const window = isLogin ? windowService.getMainWindow() : loginService.getLoginWindow()
    window!.show()
    window!.webContents.send('notification-click', notification)
  })
  electronNotification.show()
}
