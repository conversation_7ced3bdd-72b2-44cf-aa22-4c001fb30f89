<script>
  let {
    width = 20,
    height = 20,
    color = 'currentColor',
    borderWidth = 2,
    duration = 0.5
  } = $props()
</script>

<svg xmlns="http://www.w3.org/2000/svg" {width} {height} viewBox="0 0 24 24">
  <g
    fill="none"
    stroke={color}
    stroke-linecap="round"
    stroke-linejoin="round"
    stroke-width={borderWidth}
  >
    <path d="M5 5L19 19M5 19L19 5">
      <animate
        fill="freeze"
        attributeName="d"
        dur={duration * 0.4}
        values="M5 5L19 19M5 19L19 5;M5 5L19 5M5 19L19 19"
      />
    </path>
    <path d="M12 12H12" opacity="0">
      <animate
        fill="freeze"
        attributeName="d"
        begin={duration * 0.2}
        dur={duration * 0.4}
        values="M12 12H12;M5 12H19"
      />
      <set fill="freeze" attributeName="opacity" begin={duration * 0.2} to="1" />
    </path>
  </g>
</svg>
