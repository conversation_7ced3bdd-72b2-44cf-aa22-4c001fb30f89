<script lang="ts">
  import { Toaster } from 'svelte-french-toast'
  import '@assets/styles/index.css'
  import 'nprogress/nprogress.css'
  import TitleBar from '$lib/components/TitleBar.svelte'
  import { getTheme } from '$lib/stores/theme.svelte'

  let { children } = $props()

  $effect(() => {
    document.documentElement.dataset.theme = getTheme()
  })
</script>

<div class="bg-base-100 text-base-content flex h-screen flex-col">
  <Toaster position="top-center" containerStyle="top: 50px; bottom: 50px" />
  {@render children()}
</div>
