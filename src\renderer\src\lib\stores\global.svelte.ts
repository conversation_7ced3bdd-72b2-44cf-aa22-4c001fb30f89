import { IDE, ThemeMode } from '@types'
import toast from 'svelte-french-toast'

interface Settings {
  currentTheme: ThemeMode
  ide: IDE
}

export let settings = $state<Settings>({
  /** 当前主题 */
  currentTheme: ThemeMode.system,
  /** 当前 IDE */
  ide: IDE.VSCode
})

export async function initSettings() {
  const [theme, ide] = await Promise.all([window.api.getTheme(), window.api.getIDE()])
  settings.currentTheme = theme
  settings.ide = ide
}

interface EnvInfo {
  chromeVersion: string
  softwareInfo: any
  augmentLocalInfo: any
  augmentRemoteInfo: any
  augmentCurrentInfo: any
  augmentPreviewInfo: any
  extensionsList: any[]
  loading: boolean
}

/**
 * 环境信息
 */
export let envInfo = $state<EnvInfo>({
  /** chrome版本 */
  chromeVersion: '',
  /** 软件信息 */
  softwareInfo: {},
  /** 本地 Augment 信息 */
  augmentLocalInfo: {},
  /** 远程 Augment 信息 */
  augmentRemoteInfo: {},
  /** 当前 Augment 信息 */
  augmentCurrentInfo: {},
  /** 预览 Augment 信息 */
  augmentPreviewInfo: {},
  /** 扩展列表 */
  extensionsList: [],
  /** 是否正在加载 */
  loading: false
})

export async function getChromeVersion() {
  try {
    const res = await window.api.getChromeVersion()
    if (!res.success) {
      toast.error(res.message)
      return
    }
    envInfo.chromeVersion = res.data
  } catch (error: any) {
    toast.error(error.message)
  }
}

export async function getSoftwareInfo() {
  try {
    const res = await window.api.getSoftwareInfo()
    if (!res.success) {
      toast.error(res.message)
      return
    }
    envInfo.softwareInfo = res.data || {}
    if (res.data?.chromePath) {
      getChromeVersion()
    }
  } catch (error: any) {
    toast.error(error.message)
  }
}

export async function getAugmentInfo() {
  try {
    envInfo.loading = true
    const res = await window.api.getExtensionsInfo()
    if (!res.success) {
      toast.error(res.message)
      return
    }
    const data = res.data || {}
    envInfo.augmentLocalInfo = data.augmentLocalInfo || {}
    envInfo.augmentRemoteInfo = data.augmentRemoteInfo || {}
    envInfo.augmentCurrentInfo = data.augmentRemoteInfo?.currentInfo || {}
    envInfo.augmentPreviewInfo = data.augmentRemoteInfo?.previewInfo || {}
    envInfo.extensionsList = data.extensionsList || []
  } catch (error: any) {
    toast.error(error.message)
  } finally {
    envInfo.loading = false
  }
}
