{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .prettier*, prettier*, .editorconfig", "tsconfig.json": "tsconfig.node.json, tsconfig.web.json, vite.config.ts, svelte.config.mjs, svelte.config.js, electron.vite.config.ts"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[svelte]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "svelte.enable-ts-plugin": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "svelte"], "svelte.plugin.svelte.compilerWarnings": {"a11y-click-events-have-key-events": "ignore", "a11y-no-static-element-interactions": "ignore", "a11y_consider_explicit_label": "ignore", "a11y_missing_attribute": "ignore", "a11y_click_events_have_key_events": "ignore"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.organizeImports": "never"}, "editor.defaultFormatter": "esbenp.prettier-vscode"}