import Database from 'better-sqlite3'
import fs from 'fs'
import { sqlLogger } from '@main/utils/logger'

export class SQLiteConnection {
  public readonly db: Database.Database
  private readonly busyTimeout: number
  private readonly logger: typeof sqlLogger

  constructor(
    public readonly dbPath: string,
    options: {
      busyTimeout?: number
      logging?: boolean
      logger?: typeof sqlLogger
    } = {}
  ) {
    this.busyTimeout = options.busyTimeout || 5000
    this.logger = options.logger || sqlLogger

    try {
      // 初始化数据库连接
      this.db = new Database(dbPath, {
        timeout: this.busyTimeout,
        verbose: options.logging ? (sql) => this.log(sql as string) : undefined
      })

      // 性能优化设置
      this.db.pragma('journal_mode = WAL')
      this.db.pragma('synchronous = NORMAL')
      this.db.pragma('foreign_keys = ON')

      this.log(`Database connection established to ${dbPath}`)
    } catch (error) {
      this.handleError(`Connection failed: ${error}`)
      throw error
    }
  }

  protected handleError(message: string): void {
    this.logger.error(message)
  }

  protected log(message: string, level: 'info' | 'debug' | 'warn' = 'info'): void {
    this.logger[level](message)
  }

  public async backup(): Promise<void> {
    if (this.dbPath === ':memory:') {
      this.log('Skipping backup for in-memory database', 'debug')
      return
    }

    const backupPath = `${this.dbPath}.backup`
    try {
      if (fs.existsSync(this.dbPath)) {
        await fs.promises.copyFile(this.dbPath, backupPath)
        this.log(`Database backup created at: ${backupPath}`)
      } else {
        this.log('Database file not found for backup', 'warn')
      }
    } catch (error) {
      this.handleError(`Backup failed: ${error}`)
    }
  }

  public async close(): Promise<void> {
    try {
      if (!this.db?.open) {
        this.log('Database already closed', 'debug')
        return
      }

      this.log('Closing database connection...')
      await this.backup()
      this.db.close()
      this.log('Database connection closed successfully')
    } catch (error) {
      this.handleError(`Close failed: ${error}`)
      throw error
    }
  }

  public async checkIntegrity(full: boolean = false): Promise<string> {
    try {
      this.log(`Running ${full ? 'full' : 'quick'} integrity check...`, 'debug')
      const stmt = this.db.prepare(full ? 'PRAGMA integrity_check' : 'PRAGMA quick_check')
      const result = stmt.get() as any
      const status = full ? result.integrity_check : result.quick_check

      if (status === 'ok') {
        this.log('Database integrity check passed')
      } else {
        this.log(`Database integrity issues found: ${status}`, 'warn')
      }

      return status
    } catch (error) {
      this.handleError(`Integrity check failed: ${error}`)
      throw error
    }
  }

  public get isInMemory(): boolean {
    return this.dbPath === ':memory:'
  }

  public transaction<T>(fn: () => T): T {
    this.log('Beginning transaction', 'debug')
    try {
      this.db.exec('BEGIN TRANSACTION')
      const result = fn()
      this.db.exec('COMMIT')
      this.log('Transaction committed successfully', 'debug')
      return result
    } catch (error) {
      this.db.exec('ROLLBACK')
      this.handleError(`Transaction failed: ${error}`)
      throw error
    }
  }

  public async asyncTransaction<T>(fn: () => Promise<T>): Promise<T> {
    this.log('Beginning async transaction', 'debug')
    try {
      this.db.exec('BEGIN TRANSACTION')
      const result = await fn()
      this.db.exec('COMMIT')
      this.log('Async transaction committed successfully', 'debug')
      return result
    } catch (error) {
      this.db.exec('ROLLBACK')
      this.handleError(`Async transaction failed: ${error}`)
      throw error
    }
  }
}
