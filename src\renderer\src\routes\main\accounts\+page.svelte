<script lang="ts">
  import { onMount } from 'svelte'
  import Icon from '@iconify/svelte'
  import {
    getAccounts,
    accountStore,
    clearAccounts,
    addAccount,
    deleteAccount,
    updateAccount
  } from '$lib/stores/account.svelte'
  import Table from '$lib/components/Table.svelte'
  import type { AugmentAccountInsert, AugmentAccountRow, LocalAccountInfo } from '@renderer/types'
  import { AUGMENT_SESSIONS_KEY, DialogTypeEnum } from '$lib/constant'
  import toast from 'svelte-french-toast'
  import AccountDialog from '$lib/components/AccountDialog.svelte'
  import { apiGetAugmentSubscriptionInfo } from '$lib/apis/augment'
  import { truncateText } from '$lib/utils'

  let localAccountInfo = $state<LocalAccountInfo | null>(null)
  let accountDialogInfo = $state({
    open: false,
    type: DialogTypeEnum.Add,
    initData: {}
  })
  let isRefreshing = $state(false)
  // 敏感信息
  let isSensitive = $state(true)

  onMount(async () => {
    getAccounts()
    getLoggedInAccount()
    const url = new URL(window.location.href)
    const token = url.searchParams.get('token')
    const tenantUrl = url.searchParams.get('tenantUrl')
    if (token && tenantUrl) {
      add({
        token,
        tenant_url: tenantUrl
      })
    }
  })

  function truncate(text: string) {
    if (isSensitive) {
      return truncateText(text)
    }
    return text
  }

  function add(initRow?: Partial<AugmentAccountInsert>) {
    accountDialogInfo = {
      open: true,
      type: DialogTypeEnum.Add,
      initData: initRow || {}
    }
  }

  function edit(row: AugmentAccountRow) {
    accountDialogInfo = {
      open: true,
      type: DialogTypeEnum.Edit,
      initData: row
    }
  }

  function view(row: AugmentAccountRow) {
    accountDialogInfo = {
      open: true,
      type: DialogTypeEnum.View,
      initData: row
    }
  }

  async function switchAccount(row: AugmentAccountRow) {
    try {
      const info = {
        accessToken: row.token,
        tenantURL: row.tenant_url,
        scopes: JSON.parse(row.scopes)
      }
      const res = await window.api.secrets.encrypt(info)
      if (!res) {
        return toast.error('切换失败, 未加密成功')
      }
      await window.api.db.vscode.set(AUGMENT_SESSIONS_KEY, res)
      toast.success('切换成功')
      getLoggedInAccount()
    } catch (error) {
      toast.error(error.message || error)
    }
  }

  async function getLoggedInAccount() {
    try {
      const value = await window.api.db.vscode.get(AUGMENT_SESSIONS_KEY)
      if (!value) {
        return toast.error('augment插件未登录')
      }
      const res = await window.api.secrets.decrypt(value.data)
      if (!res) {
        return toast.error('augment token解析失败')
      }
      localAccountInfo = JSON.parse(res)
    } catch (error) {
      toast.error(error.message)
    }
  }

  async function submitAccount(data: AugmentAccountInsert | AugmentAccountRow) {
    const res = await apiGetAugmentSubscriptionInfo(data.tenant_url, data.token)
    const newData = {
      ...data,
      ...res
    }
    if (accountDialogInfo.type === DialogTypeEnum.Add) {
      await addAccount(newData)
      toast.success('添加成功')
    } else if (accountDialogInfo.type === DialogTypeEnum.Edit) {
      await updateAccount(newData)
      toast.success('更新成功')
    }

    getAccounts()
  }

  function closeAccountDialog() {
    accountDialogInfo = {
      open: false,
      type: DialogTypeEnum.Add,
      initData: {}
    }
  }
</script>

<div class="flex h-full flex-col">
  <div class="mb-1 flex gap-2">
    <button class="btn btn-sm btn-outline btn-primary" onclick={add}>添加账号</button>
    <button class="btn btn-sm btn-soft btn-error" onclick={clearAccounts}>清空</button>
    <div class="tooltip tooltip-bottom tooltip-info ml-auto">
      <div class="tooltip-content text-xs font-normal">
        敏感信息: {isSensitive ? '隐藏' : '显示'}
      </div>
      <button
        class="btn btn-sm btn-circle btn-soft btn-warning"
        onclick={() => (isSensitive = !isSensitive)}
      >
        <Icon
          icon={isSensitive ? 'humbleicons:eye-close' : 'humbleicons:eye'}
          width="24"
          height="24"
        />
      </button>
    </div>
    <div class="tooltip tooltip-bottom tooltip-info mr-2">
      <div class="tooltip-content text-xs font-normal">刷新</div>
      <button
        class="btn btn-sm btn-circle btn-soft btn-info {isRefreshing ? 'rotate-animation' : ''}"
        onclick={() => {
          isRefreshing = true
          getAccounts()
          setTimeout(() => {
            isRefreshing = false
          }, 500)
        }}
      >
        <Icon icon="material-symbols-light:refresh" width="24" height="24" />
      </button>
    </div>
  </div>
  <Table
    class="overflow-y-auto"
    tableClass="table-xs table-pin-rows table-pin-cols"
    tableStyle="table-layout: fixed;"
    data={accountStore.accounts}
    key="id"
    hover
    active={{
      key: 'token',
      value: localAccountInfo?.accessToken
    }}
  >
    {#snippet header()}
      <th class="w-10">ID</th>
      <td class="w-25">邮箱</td>
      <td class="w-13">额度</td>
      <td class="w-20">到期时间</td>
      <td class="w-16">token</td>
      <td class="w-16">备注</td>
      <td class="w-20">添加时间</td>
      <th class="w-20">操作</th>
    {/snippet}
    {#snippet body(row: AugmentAccountRow)}
      <th class="w-10">{row.id}</th>
      <td class="w-25 truncate select-all" title={row.email}>
        {truncate(row.email)}
      </td>
      <td class="w-13">
        {#if row.usage_balance_depleted}
          <span class="text-error font-bold">用完</span>
        {:else}
          <span class="text-success font-bold">未用完</span>
        {/if}
      </td>
      <td class="w-20">
        {#if row.usage_balance_depleted}
          <span class="text-error font-bold">未知</span>
        {:else}
          <span>{row.end_date || 'Free'}</span>
        {/if}
      </td>
      <td class="w-16 truncate select-all" title={row.token}>{truncate(row.token)}</td>
      <td class="w-16 truncate" title={row.desc}>{row.desc}</td>
      <td class="w-20">{row.created_at}</td>
      <th class="w-20">
        <div class="flex flex-wrap gap-1 py-2">
          <button class="btn btn-xs btn-outline btn-primary" onclick={() => view(row)}>详情</button>
          <button class="btn btn-xs btn-outline btn-info" onclick={() => edit(row)}>编辑</button>
          <div class="tooltip tooltip-left tooltip-info">
            <div class="tooltip-content text-xs font-normal">
              {row.token === localAccountInfo?.accessToken
                ? '无需切换'
                : row.usage_balance_depleted
                  ? '额度已用完或已过期, 无法切换'
                  : '切换成功后, 需要重启编辑器'}
            </div>
            <button
              class="btn btn-xs btn-outline btn-success"
              disabled={row.token === localAccountInfo?.accessToken || row.usage_balance_depleted}
              onclick={() => switchAccount(row)}>切号</button
            >
          </div>
          <button
            class="btn btn-xs btn-outline btn-error"
            onclick={async () => {
              const res = await deleteAccount(row.id)
              if (res) {
                toast.success('删除成功')
              }
            }}>删除</button
          >
        </div>
      </th>
    {/snippet}
  </Table>
  {#if accountDialogInfo.open}
    <AccountDialog
      type={accountDialogInfo.type}
      initData={accountDialogInfo.initData}
      {localAccountInfo}
      submit={submitAccount}
      close={closeAccountDialog}
    />
  {/if}
</div>

<style>
  :global(.hidden-scrollbar) {
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .rotate-animation {
    animation: rotate 0.5s linear;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
