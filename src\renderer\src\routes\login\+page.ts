import type { RememberLoginInfo } from '@types'
import type { PageLoad } from './$types'

export const load: PageLoad = async () => {
  const [token, rememberLoginInfo]: [string, RememberLoginInfo] = await Promise.all([
    window.api.login.getToken(),
    window.api.login.getRememberLoginInfo()
  ])

  return {
    token,
    ...rememberLoginInfo,
    username: rememberLoginInfo.isRemember ? rememberLoginInfo.username : '',
    password: rememberLoginInfo.isRemember ? rememberLoginInfo.password : ''
  }
}
