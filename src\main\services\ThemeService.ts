import { IpcChannel } from '@shared/IpcChannel'
import { ThemeMode } from '@types'
import { BrowserWindow, nativeTheme } from 'electron'

import { titleBarOverlayDark, titleBarOverlayLight } from '../constant'
import { mainLogger } from '@main/utils/logger'
import { getTheme, setTheme } from '@main/ipc/handle'

export class ThemeService {
  private theme: ThemeMode = ThemeMode.system
  private static instance: ThemeService

  constructor() {
    this.theme = getTheme()
    if (
      this.theme === ThemeMode.dark ||
      this.theme === ThemeMode.light ||
      this.theme === ThemeMode.system
    ) {
      nativeTheme.themeSource = this.theme
    } else {
      // 兼容旧版本
      setTheme(ThemeMode.system)
      nativeTheme.themeSource = ThemeMode.system
    }
    nativeTheme.on('updated', this.themeUpdatedHandler.bind(this))
  }

  public static getInstance(): ThemeService {
    if (!this.instance) {
      this.instance = new ThemeService()
    }
    return this.instance
  }

  themeUpdatedHandler() {
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed() && win.setTitleBarOverlay) {
        try {
          win.setTitleBarOverlay(
            nativeTheme.shouldUseDarkColors ? titleBarOverlayDark : titleBarOverlayLight
          )
        } catch (error) {
          // don't throw error if setTitleBarOverlay failed
          // Because it may be called with some windows have some title bar
        }
      }
      win.webContents.send(
        IpcChannel.ThemeUpdated,
        nativeTheme.shouldUseDarkColors ? ThemeMode.dark : ThemeMode.light
      )
    })
  }

  setTheme(theme: ThemeMode) {
    if (theme === this.theme) {
      return
    }
    this.theme = theme
    nativeTheme.themeSource = theme
    setTheme(theme)
    mainLogger.info('设置主题', theme)
  }
}

export const themeService = ThemeService.getInstance()
