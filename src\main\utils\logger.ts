import { APP_NAME, APP_NAME_DEV, isDev } from '@main/constant'
import dayjs from 'dayjs'
import { app } from 'electron'
import Logger from 'electron-log'
import path from 'path'

/** 主进程日志 */
const mainLogger = Logger.create({ logId: 'main' })
/** 数据库日志 */
const sqlLogger = Logger.create({ logId: 'sql' })
/** 渲染进程日志 */
const rendererLogger = Logger.create({ logId: 'renderer' })

const dateStr = dayjs().format('YYYY-MM-DD')
const userDataPath = path.join(app.getPath('appData'), isDev ? APP_NAME_DEV : APP_NAME)
const logsDir = path.join(userDataPath, 'logs', dateStr)

function initializeLoggers() {
  mainLogger.transports.file.resolvePathFn = () => path.join(logsDir, `${mainLogger.logId}.log`)
  sqlLogger.transports.file.resolvePathFn = () => path.join(logsDir, `${sqlLogger.logId}.log`)
  rendererLogger.transports.file.resolvePathFn = () =>
    path.join(logsDir, `${rendererLogger.logId}.log`)
}

initializeLoggers()

export { mainLogger, sqlLogger, rendererLogger }
