/** 主题模式 */
export enum ThemeMode {
  light = 'light',
  dark = 'dark',
  system = 'system'
}

/** 开发环境 */
export enum IDE {
  VSCode = 'Code',
  Cursor = 'Cursor'
}

export interface Shortcut {
  key: string
  shortcut: string[]
  editable: boolean
  enabled: boolean
  system: boolean
}

export interface StoreSyncAction {
  type: string
  payload: any
  meta?: {
    fromSync?: boolean
    source?: string
  }
}

export interface RememberLoginInfo {
  autoLogin?: boolean
  isRemember?: boolean
  username?: string
  password?: string
}

export interface OauthState {
  creationTime: string
  codeVerifier: string
  codeChallenge: string
  state: string
}

export interface DownloadProgressMessage {
  /** 文件名 */
  fileName: string
  /** 进度 */
  progress: number
  /** 是否准备好 */
  ready: boolean
  /** 是否成功 */
  success: boolean
  /** 是否失败 */
  error: boolean
  /** 消息 */
  message: string
}

export interface AugmentAccountRow {
  id: number
  /** 邮箱 */
  email?: string | null
  /** 令牌 */
  token: string
  /** 额度是否用完, 0 for false, 1 for true */
  usage_balance_depleted?: number
  /** 结束日期 */
  end_date?: string | null
  /** 描述 */
  desc?: string | null
  /** 创建日期 */
  created_at?: string
  /** 租户URL */
  tenant_url?: string
  /** 范围 */
  scopes?: string
}

export type AugmentAccountInsert = Omit<AugmentAccountRow, 'id'> & {
  id?: never
}

export interface LocalAccountInfo {
  /** 令牌 */
  accessToken?: string
  /** 范围 */
  scopes?: string[]
  /** 租户URL */
  tenantURL?: string
}
