import tseslint from '@electron-toolkit/eslint-config-ts'
import prettier from 'eslint-config-prettier'
import js from '@eslint/js'
import ts from 'typescript-eslint'
import globals from 'globals'
import eslintConfigPrettier from '@electron-toolkit/eslint-config-prettier'
import svelte from 'eslint-plugin-svelte'
import { fileURLToPath } from 'node:url'
import { includeIgnoreFile } from '@eslint/compat'
import svelteConfig from './svelte.config.js'

const gitignorePath = fileURLToPath(new URL('./.gitignore', import.meta.url))

export default tseslint.config(
  { ignores: ['**/node_modules', '**/dist', '**/out'] },
  includeIgnoreFile(gitignorePath),
  tseslint.configs.recommended,
  js.configs.recommended,
  ...ts.configs.recommended,
  ...svelte.configs.recommended,
  prettier,
  ...svelte.configs.prettier,
  {
    languageOptions: {
      globals: { ...globals.browser, ...globals.node }
    }
    // rules: { 'no-undef': 'off' }
  },
  {
    files: ['**/*.{js, cjs}'],
    rules: {
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-require-imports': 'off'
    }
  },
  {
    files: ['**/*.svelte', '**/*.svelte.{ts,js}'],
    languageOptions: {
      parserOptions: {
        projectService: true,
        extraFileExtensions: ['.svelte'],
        parser: ts.parser,
        svelteConfig
      }
    }
  },
  {
    files: ['**/*.{tsx,svelte}'],
    rules: {
      'svelte/no-unused-svelte-ignore': 'off',
      'svelte/no-inspect': 'off',
      'svelte/a11y_consider_explicit_label': 'off',
      'svelte/a11y-click-events-have-key-events': 'off'
    }
  },
  {
    files: ['**/*.{svelte,ts,js}'],
    rules: {
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'no-unused-vars': 'off',
      // 'no-undef': 'off',
      'prefer-const': 'off'
    }
  },
  eslintConfigPrettier
)
