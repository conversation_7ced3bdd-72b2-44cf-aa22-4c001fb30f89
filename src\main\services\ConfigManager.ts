import Store from 'electron-store'
import { type Shortcut } from '@types'
import { ZOOM_SHORTCUTS } from '@shared/config/constans'
import { setUserDataPath } from '@main/utils/paths'
import { getIDE } from '@main/ipc/handle'
import { app } from 'electron'
import { mainLogger } from '@main/utils/logger'

export enum ConfigKeys {
  Language = 'language', // 语言
  Shortcuts = 'shortcuts'
}

export class ConfigManager {
  private store: Store
  private subscribers: Map<string, Array<(newValue: any) => void>> = new Map()
  private static instance: ConfigManager
  constructor() {
    // 让config.json在本应用的userData目录下
    setUserDataPath()
    this.store = new Store()
    // 初始化时切到vscode用户目录,用于safeStorage.decrypting解密
    const ide = getIDE()
    setUserDataPath(ide)
    mainLogger.info('集成开发环境: ' + ide + ', 数据目录:' + app.getPath('userData'))
  }

  public static getInstance() {
    if (!this.instance) {
      this.instance = new ConfigManager()
    }
    return this.instance
  }

  // 获取快捷键
  getShortcuts() {
    return this.get(ConfigKeys.Shortcuts, ZOOM_SHORTCUTS) as Shortcut[] | []
  }

  // 设置快捷键
  setShortcuts(shortcuts: Shortcut[]) {
    this.setAndNotify(
      ConfigKeys.Shortcuts,
      shortcuts.filter((shortcut) => shortcut.system)
    )
  }

  openEditor() {
    this.store.openInEditor()
  }

  // 订阅
  subscribe<T>(key: string, callback: (newValue: T) => void) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, [])
    }
    this.subscribers.get(key)!.push(callback)
  }

  // 取消订阅
  unsubscribe<T>(key: string, callback: (newValue: T) => void) {
    const subscribers = this.subscribers.get(key)
    if (subscribers) {
      this.subscribers.set(
        key,
        subscribers.filter((subscriber) => subscriber !== callback)
      )
    }
  }

  // 通知订阅者
  private notifySubscribers<T>(key: string, newValue: T) {
    const subscribers = this.subscribers.get(key)
    if (subscribers) {
      subscribers.forEach((subscriber) => subscriber(newValue))
    }
  }

  // 设置并通知
  setAndNotify(key: string, value: unknown) {
    this.set(key, value, true)
  }

  set(key: string, value: unknown, isNotify: boolean = false) {
    this.store.set(key, value)
    isNotify && this.notifySubscribers(key, value)
  }

  get<T>(key: string, defaultValue?: T) {
    return this.store.get(key, defaultValue) as T
  }
}
