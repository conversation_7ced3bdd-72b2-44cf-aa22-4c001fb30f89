import puppeteer from 'puppeteer-extra'
import { <PERSON><PERSON><PERSON>, LaunchOptions, <PERSON> } from 'puppeteer-core'
import StealthPlugin from 'puppeteer-extra-plugin-stealth'
import { getChromePath } from '@main/utils/paths'
import { mainLogger } from '@main/utils/logger'

puppeteer.use(StealthPlugin())

export class PuppeteerService {
  public launchOptions: LaunchOptions = {
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--hide-scrollbars', '--mute-audio'],
    ignoreDefaultArgs: ['--enable-automation']
  }
  public browser: Browser | null = null

  constructor(options?: LaunchOptions) {
    this.launchOptions = {
      ...this.launchOptions,
      ...options
    }
  }

  public async launch(): Promise<Browser> {
    const path = this.launchOptions.executablePath || getChromePath()
    if (!path) {
      mainLogger.error('chrome不存在, 请检查chrome路径')
      throw new Error('chrome不存在, 请检查chrome路径')
    }
    const browser = await puppeteer.launch({
      ...this.launchOptions,
      executablePath: path || undefined,
      channel: path ? undefined : 'chrome'
    })
    this.browser = browser
    mainLogger.info('browser launch success')
    return browser
  }

  public async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      const browser = await this.launch()
      return browser
    }
    return this.browser
  }

  public async newPage(): Promise<Page> {
    let browser = this.browser
    if (!browser) {
      browser = await this.launch()
    }
    const page = await browser.newPage()
    return page
  }

  public async browserClose(): Promise<void> {
    if (!this.browser) return
    try {
      await this.browser.close()
      this.browser = null
      mainLogger.info('Browser closed successfully')
    } catch (error) {
      mainLogger.error('Error closing browser:', error)
      throw error
    }
  }
}
