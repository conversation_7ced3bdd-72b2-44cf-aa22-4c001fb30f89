import { app, BrowserWindow, nativeTheme } from 'electron'
import icon from '../../../build/icon.png?asset'
import { isLinux, isMac } from '@main/constant'
import { join } from 'path'
import { initSessionUserAgent } from './WebviewService'
import { is } from '@electron-toolkit/utils'
import { ConfigManager } from './ConfigManager'
import { titleBarOverlayDark, titleBarOverlayLight } from '@main/constant'
import { mainLogger } from '@main/utils/logger'
import electronServe from 'elecp-serve'

class WindowService {
  private static instance: WindowService | null = null
  private mainWindow: BrowserWindow | null = null
  private lastRendererProcessCrashTime: number = 0
  private configManager: ConfigManager

  constructor() {
    this.configManager = ConfigManager.getInstance()
  }

  public static getInstance(): WindowService {
    if (!WindowService.instance) {
      WindowService.instance = new WindowService()
    }
    return WindowService.instance
  }

  public createMainWindow(): BrowserWindow {
    mainLogger.info('创建主窗口')
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.show()
      this.mainWindow.focus()
      return this.mainWindow
    }

    this.mainWindow = new BrowserWindow({
      title: 'Augment小助手',
      width: 860,
      height: 600,
      minWidth: 600,
      minHeight: 400,
      show: false,
      autoHideMenuBar: true,
      transparent: isMac,
      vibrancy: 'sidebar',
      visualEffectState: 'active',
      titleBarStyle: 'hidden',
      frame: false,
      titleBarOverlay: nativeTheme.shouldUseDarkColors ? titleBarOverlayDark : titleBarOverlayLight,
      darkTheme: nativeTheme.shouldUseDarkColors,
      trafficLightPosition: { x: 8, y: 12 },
      ...(isLinux ? { icon } : {}),
      webPreferences: {
        preload: join(__dirname, '../preload/index.mjs'),
        sandbox: false,
        webSecurity: false, // 允许加载本地资源
        webviewTag: true,
        allowRunningInsecureContent: true, // 允许 HTTPS 页面从 HTTP URL 运行 JavaScript、CSS 或插件
        backgroundThrottling: false // 禁用背景处理
      }
    })

    this.setupMainWindow(this.mainWindow)

    //初始化MinApp网页视图的用户代理
    initSessionUserAgent()
    if (is.dev) {
      this.mainWindow.webContents.openDevTools()
    }
    return this.mainWindow
  }

  private setupMainWindow(mainWindow: BrowserWindow) {
    // 上下文菜单 暂时用不到
    // this.setupContextMenu(mainWindow)
    // 窗口事件
    this.setupWindowEvents(mainWindow)
    // 导航和点击链接行文,暂时用不到
    // this.setupWebContentsHandlers(mainWindow)
    // 窗口close closed
    this.setupWindowLifecycleEvents(mainWindow)
    // 窗口崩溃/卡死行为
    this.setupMainWindowMonitor(mainWindow)
    // 加载窗口内容
    this.loadMainWindowContent(mainWindow)
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  // 基于 electron-vite cli 的渲染器的热模块替换。用于开发时加载远程 URL，或在生产时加载本地 HTML 文件
  private loadMainWindowContent(mainWindow: BrowserWindow) {
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/main')
    } else {
      const loadURL = electronServe({
        scheme: 'main',
        directory: join(__dirname, '../renderer')
      })
      loadURL(mainWindow, undefined, '/main')
    }
  }

  private setupWindowLifecycleEvents(mainWindow: BrowserWindow) {
    mainWindow.on('close', (event) => {
      // 如果由托盘点击退出，直接退出
      if (app.isQuitting) {
        mainLogger.info('主窗口由托盘关闭')
        return app.quit()
      }
      // 切换登录时 应完全退出窗口 不走hide
      if (app.isToggle) {
        mainLogger.info('主窗口→登录窗口')
        return
      }
      event.preventDefault()
      mainWindow.hide()
      //对于Mac用户，关闭到托盘时应该隐藏Dock图标
      if (isMac) {
        app.dock?.hide()
      }
      mainLogger.info('主窗口隐藏')
    })

    mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private setupMainWindowMonitor(mainWindow: BrowserWindow) {
    mainWindow.webContents.on('render-process-gone', (_, details) => {
      mainLogger.error(`【主窗口】渲染进程崩溃: ${JSON.stringify(details)}`)
      const currentTime = Date.now()
      const lastCrashTime = this.lastRendererProcessCrashTime
      this.lastRendererProcessCrashTime = currentTime
      if (currentTime - lastCrashTime > 60 * 1000) {
        // 如果大于1分钟，则重启渲染进程
        mainWindow.webContents.reload()
      } else {
        // 如果小于1分钟，则退出应用, 可能是连续crash，需要退出应用
        app.exit(1)
      }
    })

    mainWindow.webContents.on('unresponsive', () => {
      // 在升级到electron 34后，可以获取具体js stack trace,目前只打个日志监控下
      // https://www.electronjs.org/blog/electron-34-0#unresponsive-renderer-javascript-call-stacks
      mainLogger.error('【主窗口】渲染进程无响应')
    })
  }

  private setupWindowEvents(mainWindow: BrowserWindow) {
    mainWindow.once('ready-to-show', () => {
      mainLogger.info('主窗口准备完成')
      mainWindow.show()
    })

    // 添加Escape键退出最大化的支持
    mainWindow.webContents.on('before-input-event', (event, input) => {
      // 当按下Escape键且窗口处于最大化时退出最大化
      if (input.key === 'Escape' && !input.alt && !input.control && !input.meta && !input.shift) {
        if (mainWindow.isMaximized()) {
          // 获取 shortcuts 配置
          const shortcuts = this.configManager.getShortcuts()
          const exitUnmaximizeShortcut = shortcuts.find((s) => s.key === 'exit_unmaximize')
          if (exitUnmaximizeShortcut == undefined) {
            mainWindow.unmaximize()
            return
          }
          if (exitUnmaximizeShortcut?.enabled) {
            event.preventDefault()
            mainWindow.unmaximize()
            return
          }
        }
      }
      return
    })
  }

  public showMainWindow() {
    mainLogger.info('主窗口显示')
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore()
        return
      }

      /**
       * 关于 setVisibleOnAllWorkspaces
       *
       * [macOS] Known Issue
       *  setVisibleOnAllWorkspaces true/false 不会将窗口带到当前桌面（在 Windows 上正常工作）AppleScript 可能是一个解决方案，但不值得
       *
       * [Linux] Known Issue
       *  setVisibleOnAllWorkspaces 在 Linux 环境下（特别是 KDE Wayland）会导致窗口进入"假弹出"状态
       *  因此在 Linux 环境下不执行这两行代码
       */
      if (!isLinux) {
        this.mainWindow.setVisibleOnAllWorkspaces(true)
      }

      /**
       * [macOS] 在全屏幕关闭后，当窗口再次显示时，全屏行为会变得奇怪
       * 所以我们需要明确地将其设置为FALSE。
       * 虽然其他平台没有这个问题，但这样做是一个好习惯
       *
       * 检查窗口是否可见，以防止在点击dock图标时中断全屏状态
       */
      if (this.mainWindow.isFullScreen() && !this.mainWindow.isVisible()) {
        this.mainWindow.setFullScreen(false)
      }

      this.mainWindow.show()
      this.mainWindow.focus()
      if (!isLinux) {
        this.mainWindow.setVisibleOnAllWorkspaces(false)
      }
    } else {
      this.mainWindow = this.createMainWindow()
    }
  }

  public toggleMainWindow() {
    // 在全屏状态下不应切换主窗口
    // 但如果在全屏状态下将主窗口最小化到托盘，我们可以再次显示它
    // (这是macos的一个bug，因为我们可以关闭全屏状态下的窗口，并且状态会保持不变)
    if (this.mainWindow?.isFullScreen() && this.mainWindow?.isVisible()) {
      return
    }

    if (this.mainWindow && !this.mainWindow.isDestroyed() && this.mainWindow.isVisible()) {
      if (this.mainWindow.isFocused()) {
        this.mainWindow.hide()
        app.dock?.hide()
      } else {
        this.mainWindow.focus()
      }
      return
    }

    this.showMainWindow()
  }

  public closeMainWindow() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close()
    }
  }
}

export const windowService = WindowService.getInstance()
