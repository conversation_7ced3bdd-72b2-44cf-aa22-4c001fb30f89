{"include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "src/renderer/src/types/*", "packages/shared/**/*"], "compilerOptions": {"target": "esnext", "module": "esnext", "composite": true, "sourceMap": false, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noImplicitAny": false, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "types": ["electron-vite/node"], "moduleResolution": "bundler", "baseUrl": ".", "paths": {"@main/*": ["src/main/*"], "@types": ["src/renderer/src/types/index.ts"], "@shared/*": ["packages/shared/*"]}}}