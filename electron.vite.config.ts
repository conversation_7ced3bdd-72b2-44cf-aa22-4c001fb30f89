import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import tailwindcss from '@tailwindcss/vite'
import { sveltekit } from '@sveltejs/kit/vite'
import { resolve } from 'path'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@main': resolve('src/main'),
        '@types': resolve('src/renderer/src/types'),
        '@shared': resolve('packages/shared')
      }
    },
    build: {
      rollupOptions: {
        // external: ['@libsql/client', 'bufferutil', 'utf-8-validate']
      },
      sourcemap: process.env.NODE_ENV === 'development'
    },
    optimizeDeps: {
      noDiscovery: process.env.NODE_ENV === 'development'
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@shared': resolve('packages/shared'),
        '@types': resolve('src/renderer/src/types')
      }
    },
    build: {
      sourcemap: process.env.NODE_ENV === 'development'
    }
  },
  renderer: {
    plugins: [tailwindcss(), sveltekit()],
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@shared': resolve('packages/shared')
      }
    },
    server: {
      fs: {
        allow: ['packages/shared']
      }
    }
  }
})
