<script lang="ts">
  import { Tween } from 'svelte/motion'
  import { type ThemeMode } from '@renderer/types'
  import { onMount } from 'svelte'
  import toast from 'svelte-french-toast'
  import { settings } from '$lib/stores/global.svelte'

  let tabElements = $state<HTMLElement[]>([])

  const sliderTween = new Tween(
    { left: 0, width: 0 },
    { duration: 200, easing: (t) => t } // 线性缓动
  )

  const themeSelectList = [
    { label: '浅色', value: 'light' },
    { label: '深色', value: 'dark' },
    { label: '系统', value: 'system' }
  ]

  const ideList = [
    { label: 'VSCode', value: 'Code' },
    { label: 'Cursor', value: 'Cursor' }
  ]

  function updateSliderPosition(activeTab: HTMLElement) {
    sliderTween.target = {
      left: activeTab.offsetLeft,
      width: activeTab.offsetWidth
    }
  }

  function handleTabClick(item: { value: ThemeMode }, event: MouseEvent) {
    settings.currentTheme = item.value
    const clickedTab = event.currentTarget as HTMLElement
    updateSliderPosition(clickedTab)
    window.api.setTheme(item.value)
  }

  async function getCacheSize() {
    const res = await window.api.getCacheSize()
    if (!res.success) {
      return toast.error(res.message)
    }
    toast.success(`缓存大小: ${res.data} MB`, { icon: '🍪' })
  }

  async function handleClearCache() {
    const res = await window.api.clearCache()
    if (!res.success) {
      return toast.error(res.message)
    }
    toast.success('缓存清理成功')
  }

  async function handleClearFiles() {
    const res = await window.api.clearFiles()
    if (!res.success) {
      return toast.error(res.message)
    }
    toast.success('文件清理成功')
  }

  async function handleClearBackups() {
    const res = await window.api.clearBackups()
    if (!res.success) {
      return toast.error(res.message)
    }
    toast.success('备份清理成功')
  }

  async function handleClearLogs() {
    const res = await window.api.clearLogs()
    if (!res.success) {
      return toast.error(res.message)
    }
    toast.success('日志清理成功')
  }

  $effect(() => {
    if (tabElements.length > 0) {
      const activeIndex = themeSelectList.findIndex((item) => item.value === settings.currentTheme)
      if (activeIndex >= 0) {
        sliderTween.set(
          {
            left: tabElements[activeIndex].offsetLeft,
            width: tabElements[activeIndex].offsetWidth
          },
          { instant: true }
        )
      }
    }
  })

  onMount(async () => {
    const res1 = await window.api.aes.encrypt('123456')
    console.log('res1==', res1)
    if (!res1.success) {
      return toast.error(res1.message)
    }
    const res2 = await window.api.aes.decrypt(res1.data)
    console.log('res2==', res2)
    if (!res2.success) {
      return toast.error(res2.message)
    }
  })

  $effect(async () => {
    if (!settings.ide) return
    const res = await window.api.setIDE(settings.ide)
    if (!res.success) {
      toast.error(res.message)
    }
  })

  function handleRestart() {
    window.api.restart()
  }
</script>

<div>
  <div class="divider divider-secondary text-primary">常规</div>
  <div class="flex flex-wrap items-center gap-2">
    <label class="w-auto" for="ide">IDE</label>
    <select id="ide" class="select select-primary select-sm" bind:value={settings.ide}>
      <option value="" disabled selected>请选择</option>
      {#each ideList as item (item.value)}
        <option value={item.value}>{item.label}</option>
      {/each}
    </select>
    <p class="text-xs font-bold text-red-500">切换IDE后需要重启应用</p>
    <button class="btn btn-outline btn-secondary btn-sm" onclick={handleRestart}>重启应用</button>
  </div>
  <div class="mt-4 flex items-center gap-2">
    <div>主题</div>
    <div role="tablist" class="tabs tabs-box tabs-sm relative w-fit">
      <!-- 滑块元素（会平滑移动的背景） -->
      <div
        class="tab tab-active absolute z-0 h-[calc(100%-0.25rem*2)] rounded-lg text-[var(--color-base-content)] transition-colors"
        style="left: {sliderTween.current.left}px; width: {sliderTween.current.width}px;"
      ></div>

      {#each themeSelectList as item, index (item.value)}
        <button
          bind:this={tabElements[index]}
          onclick={(e) => handleTabClick(item, e)}
          class="tab relative z-10 transition-colors"
          style:color={settings.currentTheme === item.value ? 'var(--color-base-content)' : ''}
        >
          {item.label}
        </button>
      {/each}
    </div>
  </div>

  <div class="divider divider-secondary text-primary">清理</div>
  <div class="flex flex-wrap gap-2">
    <button class="btn btn-outline btn-accent" onclick={getCacheSize}>获取缓存大小</button>
    <button class="btn btn-outline btn-accent" onclick={handleClearCache}>清理缓存</button>
    <button class="btn btn-outline btn-accent" onclick={handleClearFiles}>清理文件</button>
    <button class="btn btn-outline btn-accent" onclick={handleClearBackups}>清理备份</button>
    <button class="btn btn-outline btn-accent" onclick={handleClearLogs}>清理日志</button>
  </div>
  <div class="mt-4">
    <!-- <button class="btn btn-outline btn-secondary" onclick={handleRestart}>重启窗口</button> -->
  </div>
  <!-- <div class="card-item">
    <div class="card-body">
      <h2 class="card-title">Card Title</h2>
      <div class="">
        <button
          class="btn"
          onclick={async () => {
            const appInfo = await window.api.getAppInfo()
            console.log(appInfo)
          }}>获取应用信息</button
        >

        <button
          class="btn"
          onclick={async () => {
            const data = await window.api.db.items.getAll()
            console.log(data)
          }}>获取所有数据</button
        >

        <button
          class="btn"
          onclick={async () => {
            const data = await window.api.db.vscode.getAll()
            console.log(data)
          }}>获取vscode所有数据</button
        >
        <button
          class="btn"
          onclick={async () => {
            const data = await window.api.db.vscode.get(
              'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}'
            )
            console.log(data)
          }}>获取vscode指定key数据</button
        >

        <button
          class="btn"
          onclick={async () => {
            const data = await window.api.secrets.encrypt({
              a: 1,
              b: 2
            })
            console.log(data)
          }}>加密数据</button
        >

        <button
          class="btn"
          onclick={async () => {
            const data = `{"type":"Buffer","data":[118,49,48,176,210,186,36,1,104,0,145,89,196,42,104,114,106,204,204,6,44,120,139,72,134,18,203,190,60,46,137,76,173,112,247,161,239,46,185,9,237,165,49,104,68,164,138,193,225,169,2,217,233,76,138,19,21,105,45,9,190,54,22,8,19,192,155,9,229,183,177,35,47,235,244,37,240,167,184,66,239,79,226,255,3,180,115,205,26,196,56,121,208,217,179,191,89,253,55,64,165,233,78,180,8,38,229,46,97,97,164,139,186,220,197,82,88,68,203,192,148,68,117,210,161,122,1,13,147,141,119,49,142,198,81,230,146,6,20,110,130,226,227,15,207,179,187,172,62,221,226,207,62,53,1,214,136,251,144,84,192,134,20,20,83,154,184,72,153,248,13,184,9,149,202,21,6,112]}`
            const res = await window.api.secrets.decrypt(JSON.parse(data).data)
            console.log(res)
          }}>解密数据</button
        >
      </div>
    </div>
  </div> -->
</div>

<style>
</style>
