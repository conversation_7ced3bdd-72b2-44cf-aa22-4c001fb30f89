import { app, Menu, MenuItemConstructorOptions, nativeImage, Tray } from 'electron'
import { Config<PERSON><PERSON><PERSON>, ConfigManager } from './ConfigManager'
import { windowService } from './WindowService'
import icon from '../../../build/icon.png?asset'
import { loginService } from './LoginService'
import { mainLogger } from '@main/utils/logger'
import { getLoginState } from '@main/ipc/handle'

export class TrayService {
  private static instance: TrayService
  private tray: Tray | null = null
  private contextMenu: Menu | null = null
  private configManager: ConfigManager

  constructor() {
    this.configManager = ConfigManager.getInstance()
    this.watchConfigChanges()
    this.createTray()
    TrayService.instance = this
  }

  public static getInstance() {
    return TrayService.instance
  }

  private createTray() {
    this.destroyTray()

    const iconPath = icon
    const tray = new Tray(iconPath)

    if (process.platform === 'win32') {
      tray.setImage(iconPath)
    } else if (process.platform === 'darwin') {
      const image = nativeImage.createFromPath(iconPath)
      const resizedImage = image.resize({ width: 16, height: 16 })
      resizedImage.setTemplateImage(true)
      tray.setImage(resizedImage)
    } else if (process.platform === 'linux') {
      const image = nativeImage.createFromPath(iconPath)
      const resizedImage = image.resize({ width: 16, height: 16 })
      tray.setImage(resizedImage)
    }

    this.tray = tray

    this.updateContextMenu()

    if (process.platform === 'linux') {
      this.tray.setContextMenu(this.contextMenu)
    }

    this.tray.setToolTip('Augment小助手')

    this.tray.on('right-click', () => {
      if (this.contextMenu) {
        this.tray?.popUpContextMenu(this.contextMenu)
      }
    })

    this.tray.on('click', () => {
      const isLogin = getLoginState()
      if (isLogin) {
        windowService.showMainWindow()
      } else {
        loginService.showLoginWindow()
      }
    })
    mainLogger.info('托盘初始化')
  }

  private updateContextMenu() {
    const template = [
      {
        label: '显示窗口',
        click: () => {
          const isLogin = getLoginState()
          if (isLogin) {
            windowService.showMainWindow()
          } else {
            loginService.showLoginWindow()
          }
        }
      },
      { type: 'separator' },
      {
        label: '退出',
        click: () => this.quit()
      }
    ].filter(Boolean) as MenuItemConstructorOptions[]

    this.contextMenu = Menu.buildFromTemplate(template)
  }

  private destroyTray() {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }
  }

  private watchConfigChanges() {
    this.configManager.subscribe(ConfigKeys.Language, () => {
      this.updateContextMenu()
    })
  }

  private quit() {
    app.isQuitting = true
    app.quit()
  }
}
