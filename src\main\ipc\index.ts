import { arch } from 'node:os'
import { BrowserWindow, clipboard, ipcMain, shell } from 'electron'
import { mainLogger, rendererLogger } from '@main/utils/logger'
import { sendNotification } from '@main/services/NotificationService'
import { IpcChannel } from '@shared/IpcChannel'
import { Notification } from '@shared/notification'
import { ConfigManager } from '@main/services/ConfigManager'
import { IDE, type RememberLoginInfo, type Shortcut, ThemeMode } from '@types'
import { themeService } from '@main/services/ThemeService'
import { getFilesPath, getIDEExtensionsPath } from '@main/utils/paths'
import { getResourcePath } from '@main/utils/paths'
import { compress, decompress } from '@main/utils/zip'
import storeSyncService from '@main/services/StoreSyncService'
import { isWin } from '@main/constant'
import { toggleLoginService } from '@main/services/ToggleLoginService'
import { registerDBIpc } from './ipcDatabase'
import secrets from '@main/utils/secrets'
import {
  getRememberLoginInfo,
  getToken,
  setRememberLoginInfo,
  setToken,
  generateLoginUrl,
  verifyCode,
  clearCache,
  clearBackups,
  clearFiles,
  clearLogs,
  getCacheSize,
  getTheme,
  getIDE,
  setIDE,
  aesEncrypt,
  aesDecrypt,
  getExtensionsInfo,
  getSoftwareInfo,
  openPath,
  downloadFile
} from '@main/ipc/handle'
import { registerPuppeteerIpc } from './ipcPuppeteer'

export function registerIpc(app: Electron.App) {
  const configManager = ConfigManager.getInstance()

  // 获取应用信息
  ipcMain.handle(IpcChannel.App_Info, () => ({
    // 1.0.0
    version: app.getVersion(),
    // 是否被打包
    isPackaged: app.isPackaged,
    // x64
    arch: arch(),
    // 是否为便携版
    isPortable: isWin && 'PORTABLE_EXECUTABLE_DIR' in process.env,
    // E:\\code\\electron\\ai-trial
    appPath: app.getAppPath(),
    // E:\\code\\electron\\ai-trial\\resources
    resourcesPath: getResourcePath(),
    // E:\\code\\electron\\ai-trial\\node_modules\\electron\\dist\\electron.exe
    exePath: app.getPath('exe'),
    // E:\\code\\electron\\ai-trial\\node_modules\\electron\\dist\\electron.exe
    // modulePath: app.getPath('module'),
    // C:\\Users\\<USER>\\Users\\dami\\Desktop
    // desktopPath: app.getPath('desktop'),
    // C:\\Users\\<USER>\\Documents
    // documentsPath: app.getPath('documents'),
    // C:\\Users\\<USER>\\Downloads
    // downloadsPath: app.getPath('downloads'),
    // C:\\Users\\<USER>\\Music
    // musicPath: app.getPath('music'),
    // C:\\Users\\<USER>\\Pictures
    // picturesPath: app.getPath('pictures'),
    // C:\\Users\\<USER>\\Videos
    // videosPath: app.getPath('videos'),
    // C:\\Users\\<USER>\\AppData\\Roaming
    // dataPath: app.getPath('appData'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\ai-trial-dev
    userDataPath: app.getPath('userData'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\ai-trial-dev
    sessionPath: app.getPath('sessionData'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\ai-trial-dev\\logs
    logsPath: app.getPath('logs'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\ai-trial-dev\\Crashpad
    crashDumpsPath: app.getPath('crashDumps'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\ai-trial-dev\\Data\\Files
    filesPath: getFilesPath(),
    // C:\\Users\\<USER>\\AppData\\Local\\Temp
    // tempPath: app.getPath('temp'),
    // C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Recent
    // recentPath: app.getPath('recent'),
    // C:\\Users\\<USER>\\.vscode\\extensions
    extensionsPath: getIDEExtensionsPath()
  }))

  // 渲染进程日志
  ipcMain.handle(
    IpcChannel.Renderer_Log,
    (_, level: 'info' | 'debug' | 'warn' | 'error', ...args: any[]) => {
      return rendererLogger[level](...args)
    }
  )

  // 重启应用
  ipcMain.handle(IpcChannel.App_Restart, () => {
    app.relaunch()
    app.exit(0)
  })

  // 通知
  ipcMain.handle(IpcChannel.Notification_Send, async (_, notification: Notification) => {
    await sendNotification(notification)
  })
  // 设置语言
  ipcMain.handle(IpcChannel.App_SetLanguage, (_, language) => {
    // configManager.setLanguage(language)
  })
  // 配置修改
  ipcMain.handle(IpcChannel.Config_Set, (_, key: string, value: any, isNotify: boolean = false) => {
    configManager.set(key, value, isNotify)
  })
  // 配置获取
  ipcMain.handle(IpcChannel.Config_Get, (_, key: string) => {
    return configManager.get(key)
  })
  // 设置主题
  ipcMain.handle(IpcChannel.App_SetTheme, (_, theme: ThemeMode) => {
    themeService.setTheme(theme)
  })
  // 获取主题
  ipcMain.handle(IpcChannel.App_GetTheme, () => {
    return getTheme()
  })
  // 获取IDE
  ipcMain.handle(IpcChannel.App_GetIDE, () => {
    return getIDE()
  })
  // 设置IDE
  ipcMain.handle(IpcChannel.App_SetIDE, (_, ide: IDE) => {
    return setIDE(ide)
  })
  // 打开路径
  ipcMain.handle(IpcChannel.App_OpenPath, async (_, path: string) => {
    return await openPath(path)
  })
  // 下载文件
  ipcMain.handle(
    IpcChannel.App_DownloadFile,
    (_, url: string, fileName?: string, openDialog?: boolean) => {
      downloadFile(url, fileName, openDialog)
    }
  )
  // 获取缓存大小
  ipcMain.handle(IpcChannel.App_GetCacheSize, async () => {
    return await getCacheSize()
  })
  // 清除缓存
  ipcMain.handle(IpcChannel.App_ClearCache, async () => {
    return await clearCache()
  })
  // 清除文件
  ipcMain.handle(IpcChannel.App_ClearFiles, async () => {
    return await clearFiles()
  })
  // 清除备份
  ipcMain.handle(IpcChannel.App_ClearBackups, async () => {
    return await clearBackups()
  })
  // 清除日志
  ipcMain.handle(IpcChannel.App_ClearLogs, async () => {
    return await clearLogs()
  })
  // 字符串压缩
  ipcMain.handle(IpcChannel.Zip_Compress, (_, text: string) => compress(text))
  // 字符串解压缩
  ipcMain.handle(IpcChannel.Zip_Decompress, (_, text: Buffer) => decompress(text))

  // 快捷键更新
  ipcMain.handle(IpcChannel.Shortcuts_Update, (_, shortcuts: Shortcut[]) => {
    configManager.setShortcuts(shortcuts)
    // 刷新快捷方式注册
    // if (mainWindow) {
    //   unregisterAllShortcuts()
    //   registerShortcuts(mainWindow)
    // }
  })
  // aes加密
  ipcMain.handle(IpcChannel.Aes_Encrypt, (_, text: string) => aesEncrypt(text))
  // aes解密
  ipcMain.handle(IpcChannel.Aes_Decrypt, (_, encryptedData: string) => aesDecrypt(encryptedData))
  // 获取token
  ipcMain.handle(IpcChannel.App_GetToken, () => {
    return getToken()
  })
  // 设置token
  ipcMain.handle(IpcChannel.App_SetToken, (_, token?: string) => {
    setToken(token)
  })
  // 获取登录信息
  ipcMain.handle(IpcChannel.App_GetRememberLoginInfo, () => {
    return getRememberLoginInfo()
  })
  // 设置登录信息
  ipcMain.handle(IpcChannel.App_SetRememberLoginInfo, (_, loginInfo: RememberLoginInfo) => {
    setRememberLoginInfo(loginInfo)
  })

  // 打开配置文件
  ipcMain.handle(IpcChannel.Config_OpenEditor, () => {
    configManager.openEditor()
  })

  // 以默认方式打开外部协议URL
  ipcMain.handle(IpcChannel.Open_Website, (_, url: string) => {
    shell.openExternal(url)
  })

  // secrets-加密
  ipcMain.handle(IpcChannel.Secrets_Encrypt, (_, data: any) => {
    return secrets.encrypt(data)
  })

  // secrets-解密
  ipcMain.handle(IpcChannel.Secrets_Decrypt, (_, data: number[]) => {
    return secrets.decrypt(data)
  })

  // 生成登录链接
  ipcMain.handle(IpcChannel.App_GenerateLoginUrl, () => {
    return generateLoginUrl()
  })

  // 校验code
  ipcMain.handle(
    IpcChannel.App_VerifyCode,
    (_, code: string, tenantUrl: string, redirectUri: string) => {
      return verifyCode(code, tenantUrl, redirectUri)
    }
  )

  // 获取augment扩展信息
  ipcMain.handle(IpcChannel.App_GetAugmentInfo, () => {
    return getExtensionsInfo()
  })

  // 获取软件信息
  ipcMain.handle(IpcChannel.App_GetSoftwareInfo, () => {
    return getSoftwareInfo()
  })

  // 写入剪切板
  ipcMain.handle(IpcChannel.App_WriteText, (_, text: string) => {
    clipboard.writeText(text)
  })

  // 读取剪切板
  ipcMain.handle(IpcChannel.App_ReadText, () => {
    return clipboard.readText()
  })

  // store同步
  storeSyncService.registerIpcHandler()

  // 登录切换
  toggleLoginService.registerIpcHandler()

  // 数据库ipc注册
  registerDBIpc()

  // puppeteer ipc注册
  registerPuppeteerIpc()

  mainLogger.info('ipc注册完成')
}
