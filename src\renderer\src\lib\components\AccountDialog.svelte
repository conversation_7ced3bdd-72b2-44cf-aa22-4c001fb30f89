<script lang="ts">
  import { AccountDialogTitleOption, DialogTypeEnum } from '$lib/constant'
  import Icon from '@iconify/svelte'
  import type { AugmentAccountInsert, AugmentAccountRow, LocalAccountInfo } from '@renderer/types'
  import { onMount } from 'svelte'
  import toast from 'svelte-french-toast'

  interface Props {
    type: DialogTypeEnum
    initData?: AugmentAccountInsert | AugmentAccountRow
    localAccountInfo?: LocalAccountInfo
    submit?: (data: AugmentAccountInsert | AugmentAccountRow) => Promise<void>
    close?: () => void
  }

  let {
    type = DialogTypeEnum.Add,
    initData = {},
    localAccountInfo = {},
    submit,
    close
  }: Props = $props()
  let data = $state<AugmentAccountInsert | AugmentAccountRow>({
    usage_balance_depleted: 0,
    tenant_url: 'https://d9.api.augmentcode.com/',
    scopes: '["email"]',
    ...initData
  })
  let dialogRef = $state<HTMLDialogElement>()
  let title = $derived(AccountDialogTitleOption[1][type])
  let errorInfo = $state<Record<keyof AugmentAccountInsert, string>>({})
  let loading = $state(false)

  function openDialog() {
    if (!dialogRef.open) {
      dialogRef.showModal()
    }
  }

  function closeDialog() {
    if (!dialogRef.open) {
      return
    }
    dialogRef.close()
    close?.()
  }

  function validate() {
    let valid = true
    if (!(data.token || '').trim()) {
      delete data.token
    }
    if (!(data.tenant_url || '').trim()) {
      delete data.tenant_url
    }
    if (!data.token) {
      errorInfo.token = 'Token不能为空'
      valid = false
    } else {
      errorInfo.token = undefined
    }
    if (!data.tenant_url) {
      errorInfo.tenant_url = 'URL不能为空'
      valid = false
    } else {
      errorInfo.tenant_url = undefined
    }
    return valid
  }

  async function handleSubmit() {
    if (!validate()) {
      return
    }
    try {
      loading = true
      await submit?.($state.snapshot(data))
      closeDialog()
    } catch (error) {
      const err = error.message || error || '未知错误'
      toast.error(err)
      errorInfo.token = err
    } finally {
      loading = false
    }
  }

  onMount(() => {
    openDialog()
  })
</script>

<dialog bind:this={dialogRef} class="modal" closedby="none">
  <div class="modal-box">
    <form method="dialog">
      <button class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2" onclick={closeDialog}
        >✕</button
      >
    </form>
    {#if type !== DialogTypeEnum.View}
      <h3 class="text-lg font-bold">
        {title}
      </h3>
    {/if}
    <div class="pt-4">
      {#if type === DialogTypeEnum.View}
        <div class="flex flex-col gap-2">
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">ID:</div>
            <div>{data.id}</div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">邮箱:</div>
            <div class="flex-1 break-all select-all" title={data.email}>{data.email}</div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">备注:</div>
            <div class="flex-1 break-all" title={data.desc}>{data.desc}</div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">额度:</div>
            {#if data.usage_balance_depleted}
              <div class="badge badge-soft badge-error rounded-lg">用完</div>
            {:else}
              <div class="badge badge-soft badge-success rounded-lg">未用完</div>
            {/if}
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">到期时间:</div>
            {#if data.end_date}
              <div>{data.end_date}</div>
            {:else}
              <div class="badge badge-soft badge-error rounded-lg">未知</div>
            {/if}
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">Token:</div>
            <div class="flex-1 break-all select-all" title={data.token}>{data.token}</div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">URL:</div>
            <div class="text-info flex-1 break-all underline select-all" title={data.tenant_url}>
              {data.tenant_url}
            </div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">Scopes:</div>
            <div>{data.scopes}</div>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-18 text-right">添加时间:</div>
            <div>{data.created_at}</div>
          </div>
        </div>
      {:else}
        <div class="flex flex-col gap-2">
          <div class="flex items-center gap-2">
            <label class="w-14 text-right" for="email">邮箱</label>
            <input id="email" class="input input-info flex-1" type="text" bind:value={data.email} />
            <div class="tooltip tooltip-left tooltip-info" data-tip="仅用于备注和展示">
              <Icon
                class="hover:text-base-content text-gray-500 transition-colors"
                icon="material-symbols:help-outline"
                width="20"
                height="20"
              />
            </div>
          </div>
          <div class="flex items-center gap-2">
            <label class="w-14 text-right" for="desc">描述</label>
            <input id="desc" class="input input-info flex-1" type="text" bind:value={data.desc} />
            <div class="tooltip tooltip-left tooltip-info" data-tip="仅用于备注和展示">
              <Icon
                class="hover:text-base-content text-gray-500 transition-colors"
                icon="material-symbols:help-outline"
                width="20"
                height="20"
              />
            </div>
          </div>
          <div class="flex items-center gap-2">
            <label class="w-14 text-right" for="token">Token<span class="text-error">*</span></label
            >
            <div class="flex flex-1 flex-col">
              <input
                id="token"
                class="input {errorInfo.token ? 'input-error' : 'input-info'} w-full"
                type="text"
                bind:value={data.token}
                placeholder="请输入Token"
                oninput={validate}
              />
              <p class="validator-error" style:display={errorInfo.token ? 'block' : 'none'}>
                {errorInfo.token}
              </p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <label class="w-14 text-right" for="tenant_url"
              >URL<span class="text-error">*</span>
            </label>
            <div
              class="tooltip tooltip-top tooltip-info flex flex-1 flex-col"
              data-tip="用于augment插件接口请求URL, 插件登录时会返回此URL(随机前缀), token无法在不同的URL下使用"
            >
              <input
                id="tenant_url"
                class="input {errorInfo.tenant_url ? 'input-error' : 'input-info'} w-full"
                type="text"
                bind:value={data.tenant_url}
                placeholder="请输入URL"
                oninput={validate}
              />
              <p class="validator-error" style:display={errorInfo.tenant_url ? 'block' : 'none'}>
                {errorInfo.tenant_url}
              </p>
            </div>
          </div>
        </div>
      {/if}
    </div>
    {#if type !== DialogTypeEnum.View}
      <div class="modal-action">
        <div
          class="tooltip tooltip-top tooltip-info mr-auto"
          data-tip={!localAccountInfo?.accessToken ? 'augment插件未登录' : ''}
        >
          <button
            class="btn btn-sm btn-outline btn-secondary"
            disabled={!localAccountInfo?.accessToken}
            onclick={() => {
              data = {
                ...$state.snapshot(data),
                token: localAccountInfo?.accessToken,
                scopes: JSON.stringify(localAccountInfo?.scopes),
                tenant_url: localAccountInfo?.tenantURL
              }
            }}>获取本机已登录token</button
          >
        </div>
        <button class="btn" onclick={closeDialog}>取消</button>
        <button class="btn btn-primary" disabled={loading} onclick={handleSubmit}>
          <span class={loading ? 'loading loading-spinner' : ''}></span>
          确定
        </button>
      </div>
    {/if}
  </div>
</dialog>
