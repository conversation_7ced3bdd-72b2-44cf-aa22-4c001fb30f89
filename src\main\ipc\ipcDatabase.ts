import { IpcChannel } from '@shared/IpcChannel'
import { ipcMain } from 'electron'
import { database, vscodeDatabase } from '@main/db'
import type { AugmentAccountRow, AugmentAccountInsert } from '@types'

export function registerDBIpc() {
  // ItemTable-获取所有数据
  ipcMain.handle(IpcChannel.DB_ItemTable_GetAll, () => {
    return database.items.getAll()
  })
  // ItemTable-获取指定key数据
  ipcMain.handle(IpcChannel.DB_ItemTable_Get, (_, key: string) => {
    return database.items.get(key)
  })

  // ItemTable-设置数据
  ipcMain.handle(IpcChannel.DB_ItemTable_Set, (_, key: string, item: any) => {
    return database.items.set(key, item)
  })

  // ItemTable-删除数据
  ipcMain.handle(IpcChannel.DB_ItemTable_Delete, (_, key: string) => {
    return database.items.delete(key)
  })

  // ItemTable-批量设置数据
  ipcMain.handle(IpcChannel.DB_ItemTable_BatchSet, (_, items: Map<string, any>) => {
    return database.items.batchSet(items)
  })

  // AugmentAccount-获取所有数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_GetAll, () => {
    return database.augmentAccount.getAll()
  })
  // AugmentAccount-获取指定key数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_Get, (_, value: string | number, key?: string) => {
    return database.augmentAccount.get(value, key)
  })

  // AugmentAccount-设置数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_Set, (_, row: AugmentAccountInsert) => {
    return database.augmentAccount.set(row)
  })

  // AugmentAccount-更新数据
  ipcMain.handle(
    IpcChannel.DB_AugmentAccount_Update,
    (_, row: Partial<AugmentAccountRow> & { id: number }) => {
      return database.augmentAccount.update(row)
    }
  )

  // AugmentAccount-删除数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_Delete, (_, value: string | number, key?: string) => {
    return database.augmentAccount.delete(value, key)
  })

  // AugmentAccount-批量设置数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_BatchSet, (_, rows: Partial<AugmentAccountRow>[]) => {
    return database.augmentAccount.batchSet(rows)
  })

  // AugmentAccount-清空数据
  ipcMain.handle(IpcChannel.DB_AugmentAccount_Clear, () => {
    return database.augmentAccount.clear()
  })

  // VSCode-获取所有数据
  ipcMain.handle(IpcChannel.DB_VSCode_GetAll, () => {
    return vscodeDatabase.items.getAll()
  })
  // VSCode-获取指定key数据
  ipcMain.handle(IpcChannel.DB_VSCode_Get, (_, key: string) => {
    return vscodeDatabase.items.get(key)
  })

  // VSCode-设置数据
  ipcMain.handle(IpcChannel.DB_VSCode_Set, (_, key: string, item: any) => {
    return vscodeDatabase.items.set(key, item)
  })

  // VSCode-删除数据
  ipcMain.handle(IpcChannel.DB_VSCode_Delete, (_, key: string) => {
    return vscodeDatabase.items.delete(key)
  })

  // VSCode-批量设置数据
  ipcMain.handle(IpcChannel.DB_VSCode_BatchSet, (_, items: Map<string, any>) => {
    return vscodeDatabase.items.batchSet(items)
  })
}
