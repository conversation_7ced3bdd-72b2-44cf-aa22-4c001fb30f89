import { mainLogger } from '@main/utils/logger'

export interface IpcResponse<T> {
  success: boolean
  data: T
  message?: string
}

export function success<T>(data: T, message?: string): IpcResponse<T> {
  return {
    success: true,
    data,
    message
  }
}

export function failure(message: string, flag?: string): IpcResponse<null> {
  mainLogger.error('ipc failure:', flag ? `${flag}: ${message}` : message)
  return {
    success: false,
    data: null,
    message
  }
}
