import * as crypto from 'crypto'

/**
 * 加密函数
 * @param text 要加密的文本
 * @param secretKey 密钥
 * @param iv 初始化向量
 * @returns 加密后的数据
 */
export function encrypt(
  text: string,
  secretKey: string,
  iv?: string
): { iv: string; encryptedData: string } {
  const _iv = iv ? Buffer.from(iv, 'hex') : crypto.randomBytes(16)
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(secretKey), _iv)
  let encrypted = cipher.update(text, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  return {
    iv: _iv.toString('hex'),
    encryptedData: encrypted
  }
}

/**
 * 解密函数
 * @param encryptedData 加密数据
 * @param iv 初始化向量
 * @param secretKey 密钥
 * @returns 解密后的数据
 */
export function decrypt(encryptedData: string, iv: string, secretKey: string): string {
  const decipher = crypto.createDecipheriv(
    'aes-256-cbc',
    Buffer.from(secretKey),
    Buffer.from(iv, 'hex')
  )
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  return decrypted
}

const aes = {
  encrypt,
  decrypt
}

export default aes
