import axios from 'axios'
import toast from 'svelte-french-toast'
import nprogress from 'nprogress'
import rendererLogger from './logger'

let request = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 6000
})

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    nprogress.start()
    const token = await window.api.login.getToken()
    if (token) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    rendererLogger.error('request error: ', error.message)
    toast.error('请求发送失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    nprogress.done()
    const hideToast = response.config.headers['hide-toast']
    const data = response.data
    if (!data.status) {
      const errorMsg = data.message || '请求失败'
      !hideToast && toast.error(errorMsg)
      return Promise.reject(new Error(errorMsg))
    }
    return data
  },
  (error) => {
    nprogress.done()
    const hideToast = error?.config?.headers?.['hide-toast']
    if (!error.response) {
      !hideToast && toast.error('网络错误')
      return Promise.reject(error)
    }
    switch (error.response.status) {
      // 未登录 或 token错误/过期
      case 401:
        !hideToast &&
          toast.error((error.response.data?.errors || []).join('\n'), {
            icon: '❌'
          })
        // window.api.login.setToken()
        setTimeout(() => {
          window.api.login.toggleLoginState()
        }, 2000)
        break
      default:
        !hideToast && toast.error((error.response.data?.errors || []).join('\n'))
    }
    return Promise.reject(error)
  }
)

export default request
