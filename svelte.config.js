import { vitePreprocess } from '@sveltejs/vite-plugin-svelte'
import adapter from '@sveltejs/adapter-static'

/** @type {import('@sveltejs/kit').Config} */
export default {
  // Consult https://svelte.dev/docs#compile-time-svelte-preprocess
  // for more information about preprocessors
  preprocess: vitePreprocess(),
  kit: {
    adapter: adapter({
      pages: 'out/renderer',
      assets: 'out/renderer',
      fallback: 'index.html'
      // precompress: false,
      // strict: true
    }),
    files: {
      assets: 'build',
      lib: 'src/renderer/src/lib',
      routes: 'src/renderer/src/routes',
      appTemplate: 'src/renderer/index.html'
      // errorTemplate: 'src/renderer/error.html'
    },
    alias: {
      $lib: 'src/renderer/src/lib',
      '@renderer': 'src/renderer/src',
      '@shared': 'packages/shared',
      '@assets': 'src/renderer/src/assets',
      '@types': 'src/renderer/src/types'
    }
  }
}
