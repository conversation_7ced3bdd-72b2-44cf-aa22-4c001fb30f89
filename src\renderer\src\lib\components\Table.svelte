<script lang="ts">
  import type { Snippet } from 'svelte'
  import type { ClassValue } from 'svelte/elements'

  interface Props {
    class?: ClassValue
    tableClass?: ClassValue
    thClass?: ClassValue
    tbClass?: ClassValue
    tfClass?: ClassValue
    rowClass?: ClassValue
    style?: string
    tableStyle?: string
    /** 表头 */
    header?: Snippet
    /** 内容 */
    body: Snippet
    /** 页脚 */
    foot?: Snippet
    /** 斑纹 默认false */
    stripe?: boolean
    /** 悬停 默认false --row-hover-bg可设置悬停背景色 */
    hover?: boolean
    /** 数据 */
    data: any[]
    /** key 默认id */
    key?: string
    /** 选中行的key */
    active?: {
      key: string
      value: any
    }
  }
  let {
    header,
    body,
    foot,
    data,
    stripe = false,
    hover = false,
    key = 'id',
    active,
    ...rest
  }: Props = $props()
</script>

<div id="table" class={['', rest.class]} style={rest.style}>
  <table
    class={[
      'table',
      {
        'table-zebra': stripe
      },
      rest.tableClass
    ]}
    style={rest.tableStyle}
  >
    {#if header}
      <thead class={rest.thClass}>
        <tr>
          {@render header()}
        </tr>
      </thead>
    {/if}
    <tbody class={rest.tbClass}>
      {#each data as row (row[key])}
        <tr
          class={[
            'group [&_td]:transition-colors [&_th]:transition-colors',
            hover && (!active?.value || active.value !== row[active.key])
              ? '[&_td]:group-hover:bg-[var(--row-hover-bg)] [&_th]:group-hover:bg-[var(--row-hover-bg)]'
              : '',
            // th和td都应用背景色
            active?.value && active.value === row[active.key]
              ? '[&_td]:bg-[var(--row-active-bg)] [&_th]:bg-[var(--row-active-bg)]'
              : '',
            rest.rowClass
          ]}>{@render body(row)}</tr
        >
      {/each}
    </tbody>
    {#if foot}
      <tfoot class={rest.tfClass}>
        <tr>
          {@render foot()}
        </tr>
      </tfoot>
    {/if}
  </table>
</div>

<style>
  #table {
    /** 行悬停背景色 */
    --row-hover-bg: var(--color-base-200);
    /** 选中行背景色 */
    --row-active-bg: #eff7fe;
  }
</style>
