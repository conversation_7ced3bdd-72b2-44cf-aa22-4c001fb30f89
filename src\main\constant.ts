export const isMac = process.platform === 'darwin'
export const isWin = process.platform === 'win32'
export const isLinux = process.platform === 'linux'
export const isDev = process.env.NODE_ENV === 'development'
export const isPortable = isWin && 'PORTABLE_EXECUTABLE_DIR' in process.env

/** 应用名称 */
export const APP_NAME = 'ai-trial'
/** 应用名称-开发环境 */
export const APP_NAME_DEV = 'ai-trial-dev'
/** 协议名称 */
export const PROTOCOL_NAME = 'ai-trial'

/** 标题栏样式-暗色 */
export const titleBarOverlayDark = {
  height: 40,
  color: 'rgba(255,255,255,0)',
  symbolColor: '#fff'
}

/** 标题栏样式-亮色 */
export const titleBarOverlayLight = {
  height: 40,
  color: 'rgba(255,255,255,0)',
  symbolColor: '#000'
}
