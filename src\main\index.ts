import 'dotenv/config'
import { mainLogger } from './utils/logger'
import { app } from 'electron'
import { electronApp, optimizer } from '@electron-toolkit/utils'
import { APP_NAME, isDev, isWin, PROTOCOL_NAME } from './constant'
import { loginService } from './services/LoginService'
// import { replaceDevtoolsFont } from './utils/windowUtil'
import installExtension, {
  REACT_DEVELOPER_TOOLS,
  REDUX_DEVTOOLS
} from 'electron-devtools-installer'
import { registerIpc } from './ipc'
import { TrayService } from './services/TrayService'
import { windowService } from './services/WindowService'
import { getLoginState } from './ipc/handle'
import { database, vscodeDatabase } from './db'
import { setUserDataPath } from './utils/paths'
import { registerProtocolClient, parseProtocolParams } from './services/ProtocolClient'
import initScheduleTasks from './tasks'

// 禁用 Chromium 的窗口动画这样做的主要目的是避免显示时透明窗口闪烁（特别是在 Windows 的 SelectionAssistant 工具栏上）已知问题： https://github.com/electron/electron/issues/12130#issuecomment-627198990
if (isWin) {
  app.commandLine.appendSwitch('wm-window-animations-disabled')
}

// 在生产模式下，全局处理未捕获的异常和未处理的拒绝
if (!isDev) {
  // 处理未被捕获的异常
  process.on('uncaughtException', (error) => {
    mainLogger.error('Uncaught Exception:', error)
  })

  // 处理未被捕获的拒绝
  process.on('unhandledRejection', (reason, promise) => {
    mainLogger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  })
}

// 检查单实例锁
if (!app.requestSingleInstanceLock()) {
  mainLogger.warn('单实例锁')
  app.quit()
  process.exit(0)
}

app.whenReady().then(async () => {
  setUserDataPath()
  // 为 Windows 设置应用程序用户模型 ID
  electronApp.setAppUserModelId('com.dami.' + APP_NAME)

  // 方式1: 如果已经登录, 则显示主窗口
  const isLogin = getLoginState()
  if (isLogin) {
    windowService.createMainWindow()
  } else {
    loginService.createLoginWindow()
  }
  // 方式2: 先显示登录窗口, 再自动登录
  // loginService.showLoginWindow()

  new TrayService() // 托盘

  app.on('activate', function () {
    const isLogin = getLoginState()
    if (isLogin) {
      const loginWindow = loginService.getLoginWindow()
      if (!loginWindow || loginWindow.isDestroyed()) {
        loginService.createLoginWindow()
      } else {
        loginService.showLoginWindow()
      }
      mainLogger.info('登录窗口激活')
    } else {
      const mainWindow = windowService.getMainWindow()
      if (!mainWindow || mainWindow.isDestroyed()) {
        windowService.createMainWindow()
      } else {
        windowService.showMainWindow()
      }
      mainLogger.info('主窗口激活')
    }
  })

  // registerShortcuts(mainWindow) 注册快捷键

  registerIpc(app) // 注册ipc

  // replaceDevtoolsFont(loginWindow) // 替换devtools字体

  // 开发环境给devtools安装扩展
  if (isDev) {
    installExtension([REDUX_DEVTOOLS, REACT_DEVELOPER_TOOLS])
      .then((name) => mainLogger.info(`Added Extension:  ${name}`))
      .catch((err) => mainLogger.error('An error occurred: ', err))
  }

  initScheduleTasks() // 初始化定时任务
})

registerProtocolClient(app) // 注册自定义协议

// macOS专用：处理应用程序已在运行时的协议
app.on('open-url', (event, url) => {
  event.preventDefault()
  parseProtocolParams(url)
})

// 当应用程序启动时，如果已经有一个实例正在运行，则将该实例的窗口带到前台
app.on('second-instance', (_event, argv) => {
  const isLogin = getLoginState()
  if (isLogin) {
    windowService.showMainWindow()
  } else {
    loginService.showLoginWindow()
  }
  // Windows/Linux 的协议处理程序commandLine 是一个字符串数组，最后一项可能是 URL
  const url = argv.find((arg) => arg.startsWith(PROTOCOL_NAME + '://'))
  if (url) parseProtocolParams(url)
})

// 开发环境支持打开devtools
app.on('browser-window-created', (_, window) => {
  optimizer.watchWindowShortcuts(window)
})

app.on('before-quit', () => {
  mainLogger.info('before-quit')
})

app.on('will-quit', () => {
  mainLogger.info('will-quit')
  database.close()
  vscodeDatabase.close()
})
